# برمبت نظام إدارة مبيعات السيارات الجديدة والمستعملة

## نظرة عامة على المشروع

يهدف هذا المشروع إلى تطوير نظام شامل لإدارة مبيعات السيارات الجديدة والمستعملة باستخدام لغة البرمجة C# مع إطار العمل .NET Framework أو .NET Core. يتضمن النظام إدارة العملاء، المبيعات، الدفعات، التقارير، النسخ الاحتياطية، الأرشفة، وإدارة المستخدمين مع نظام صلاحيات متقدم.

## المتطلبات الوظيفية الأساسية

### 1. إدارة السيارات
- إضافة وتعديل وعرض بيانات السيارات الجديدة والمستعملة
- تصنيف السيارات حسب النوع (جديدة/مستعملة)
- إدارة تفاصيل السيارة (الماركة، الموديل، السنة، اللون، رقم الشاسيه، رقم المحرك، السعر)
- ربط السيارات بالملفات والوثائق ذات الصلة

### 2. إدارة العملاء
- إضافة وتعديل وعرض بيانات العملاء
- منع حذف العملاء المرتبطين بعمليات تقسيط
- إمكانية استبدال العميل بعميل آخر في حالة التقسيط
- ربط العملاء بملفاتهم الشخصية والوثائق

### 3. نظام المبيعات والدفع
- دعم طرق الدفع المختلفة (كاش، تقسيط)
- إدارة الأقساط والدفعات للمبيعات بالتقسيط
- تتبع حالة الدفع لكل عملية بيع
- إنشاء جداول سداد مخصصة لكل عميل

### 4. نظام الطباعة والتقارير
- طباعة كشف حساب العميل بتصميم احترافي
- تضمين شعار المعرض وبياناته في جميع التقارير
- تقارير المبيعات اليومية والشهرية والسنوية
- تقارير الأقساط المستحقة والمتأخرة

### 5. النسخ الاحتياطية والأرشفة
- إنشاء نسخ احتياطية تلقائية ويدوية من قاعدة البيانات
- استرجاع النسخ الاحتياطية بسهولة
- نظام أرشفة احترافي لملفات السيارات والعملاء
- عرض وإدارة الملفات المؤرشفة

### 6. إدارة المستخدمين والصلاحيات
- ثلاثة مستويات من المستخدمين: مطور، مدير، مندوب مبيعات
- صلاحيات المطور: التحكم الكامل في البرنامج وتجديده بكلمة "abrar"
- صلاحيات المدير: إدارة النشاط الكامل عدا صلاحيات المطور
- صلاحيات مندوب المبيعات: محددة حسب ما يحدده المدير

### 7. نظام الحماية والترخيص
- منع تنشيط البرنامج على أجهزة متعددة إلا بعد تفعيل الاشتراك
- نظام اشتراك متنوع (سنوي، شهري، دائم)
- حماية البرنامج من النسخ غير المشروع

## التقنيات المستخدمة

### لغة البرمجة والإطار
- **لغة البرمجة**: C#
- **إطار العمل**: .NET Framework 4.8 أو .NET 6/7/8
- **نوع التطبيق**: Windows Forms Application أو WPF Application

### قاعدة البيانات
- **قاعدة البيانات الأساسية**: SQL Server Express أو SQL Server
- **قاعدة بيانات بديلة**: SQLite للنشر المحمول
- **تقنية الوصول للبيانات**: Entity Framework Core أو ADO.NET

### مكتبات إضافية
- **التقارير**: Crystal Reports أو ReportViewer
- **الطباعة**: System.Drawing.Printing
- **الأرشفة**: System.IO.Compression
- **الحماية**: System.Security.Cryptography
- **واجهة المستخدم**: DevExpress أو Telerik (اختياري)




## هيكل المشروع

```
CarSalesSystem/
├── CarSalesSystem.sln
├── CarSalesSystem.Core/
│   ├── Models/
│   │   ├── User.cs
│   │   ├── Customer.cs
│   │   ├── Car.cs
│   │   ├── Sale.cs
│   │   ├── Payment.cs
│   │   ├── Installment.cs
│   │   └── Archive.cs
│   ├── Interfaces/
│   │   ├── IUserRepository.cs
│   │   ├── ICustomerRepository.cs
│   │   ├── ICarRepository.cs
│   │   ├── ISaleRepository.cs
│   │   └── IArchiveRepository.cs
│   └── Enums/
│       ├── UserRole.cs
│       ├── CarCondition.cs
│       ├── PaymentMethod.cs
│       └── SubscriptionType.cs
├── CarSalesSystem.Data/
│   ├── Context/
│   │   └── CarSalesDbContext.cs
│   ├── Repositories/
│   │   ├── UserRepository.cs
│   │   ├── CustomerRepository.cs
│   │   ├── CarRepository.cs
│   │   ├── SaleRepository.cs
│   │   └── ArchiveRepository.cs
│   └── Migrations/
├── CarSalesSystem.Business/
│   ├── Services/
│   │   ├── UserService.cs
│   │   ├── CustomerService.cs
│   │   ├── CarService.cs
│   │   ├── SaleService.cs
│   │   ├── PaymentService.cs
│   │   ├── ReportService.cs
│   │   ├── BackupService.cs
│   │   ├── ArchiveService.cs
│   │   └── LicenseService.cs
│   └── Helpers/
│       ├── EncryptionHelper.cs
│       ├── PrintHelper.cs
│       └── FileHelper.cs
├── CarSalesSystem.UI/
│   ├── Forms/
│   │   ├── MainForm.cs
│   │   ├── LoginForm.cs
│   │   ├── UserManagementForm.cs
│   │   ├── CustomerForm.cs
│   │   ├── CarForm.cs
│   │   ├── SaleForm.cs
│   │   ├── PaymentForm.cs
│   │   ├── ReportsForm.cs
│   │   ├── BackupForm.cs
│   │   └── ArchiveForm.cs
│   ├── UserControls/
│   │   ├── CustomerListControl.cs
│   │   ├── CarListControl.cs
│   │   └── SaleListControl.cs
│   └── Resources/
│       ├── Images/
│       └── Reports/
└── CarSalesSystem.Reports/
    ├── CustomerStatement.rdlc
    ├── SalesReport.rdlc
    ├── InstallmentReport.rdlc
    └── InventoryReport.rdlc
```

## تصميم قاعدة البيانات

### جدول المستخدمين (Users)
```sql
CREATE TABLE Users (
    UserId INT IDENTITY(1,1) PRIMARY KEY,
    Username NVARCHAR(50) NOT NULL UNIQUE,
    PasswordHash NVARCHAR(255) NOT NULL,
    FullName NVARCHAR(100) NOT NULL,
    Email NVARCHAR(100),
    Phone NVARCHAR(20),
    Role INT NOT NULL, -- 0: Developer, 1: Manager, 2: SalesAgent
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    LastLoginDate DATETIME2,
    CreatedBy INT,
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
);
```

### جدول العملاء (Customers)
```sql
CREATE TABLE Customers (
    CustomerId INT IDENTITY(1,1) PRIMARY KEY,
    CustomerCode NVARCHAR(20) NOT NULL UNIQUE,
    FullName NVARCHAR(100) NOT NULL,
    NationalId NVARCHAR(20) UNIQUE,
    Phone NVARCHAR(20) NOT NULL,
    AlternatePhone NVARCHAR(20),
    Email NVARCHAR(100),
    Address NVARCHAR(500),
    City NVARCHAR(50),
    DateOfBirth DATE,
    Occupation NVARCHAR(100),
    MonthlyIncome DECIMAL(18,2),
    IsActive BIT NOT NULL DEFAULT 1,
    CanBeDeleted BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT NOT NULL,
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    Notes NVARCHAR(1000),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),
    FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId)
);
```

### جدول السيارات (Cars)
```sql
CREATE TABLE Cars (
    CarId INT IDENTITY(1,1) PRIMARY KEY,
    CarCode NVARCHAR(20) NOT NULL UNIQUE,
    Brand NVARCHAR(50) NOT NULL,
    Model NVARCHAR(50) NOT NULL,
    Year INT NOT NULL,
    Color NVARCHAR(30),
    ChassisNumber NVARCHAR(50) UNIQUE,
    EngineNumber NVARCHAR(50),
    Condition INT NOT NULL, -- 0: New, 1: Used
    PurchasePrice DECIMAL(18,2),
    SalePrice DECIMAL(18,2) NOT NULL,
    Mileage INT,
    FuelType NVARCHAR(20),
    Transmission NVARCHAR(20),
    EngineCapacity NVARCHAR(20),
    IsAvailable BIT NOT NULL DEFAULT 1,
    IsSold BIT NOT NULL DEFAULT 0,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT NOT NULL,
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    SoldDate DATETIME2,
    Notes NVARCHAR(1000),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),
    FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId)
);
```

### جدول المبيعات (Sales)
```sql
CREATE TABLE Sales (
    SaleId INT IDENTITY(1,1) PRIMARY KEY,
    SaleNumber NVARCHAR(20) NOT NULL UNIQUE,
    CustomerId INT NOT NULL,
    CarId INT NOT NULL,
    SaleDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    TotalAmount DECIMAL(18,2) NOT NULL,
    DownPayment DECIMAL(18,2) NOT NULL DEFAULT 0,
    RemainingAmount DECIMAL(18,2) NOT NULL,
    PaymentMethod INT NOT NULL, -- 0: Cash, 1: Installment
    InstallmentMonths INT,
    MonthlyInstallment DECIMAL(18,2),
    InterestRate DECIMAL(5,2),
    IsCompleted BIT NOT NULL DEFAULT 0,
    SalesAgentId INT NOT NULL,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    Notes NVARCHAR(1000),
    FOREIGN KEY (CustomerId) REFERENCES Customers(CustomerId),
    FOREIGN KEY (CarId) REFERENCES Cars(CarId),
    FOREIGN KEY (SalesAgentId) REFERENCES Users(UserId)
);
```

### جدول الدفعات (Payments)
```sql
CREATE TABLE Payments (
    PaymentId INT IDENTITY(1,1) PRIMARY KEY,
    SaleId INT NOT NULL,
    PaymentNumber NVARCHAR(20) NOT NULL,
    PaymentDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    Amount DECIMAL(18,2) NOT NULL,
    PaymentMethod NVARCHAR(50), -- Cash, Check, Bank Transfer
    ReferenceNumber NVARCHAR(50),
    ReceivedBy INT NOT NULL,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    Notes NVARCHAR(500),
    FOREIGN KEY (SaleId) REFERENCES Sales(SaleId),
    FOREIGN KEY (ReceivedBy) REFERENCES Users(UserId)
);
```

### جدول الأقساط (Installments)
```sql
CREATE TABLE Installments (
    InstallmentId INT IDENTITY(1,1) PRIMARY KEY,
    SaleId INT NOT NULL,
    InstallmentNumber INT NOT NULL,
    DueDate DATE NOT NULL,
    Amount DECIMAL(18,2) NOT NULL,
    PaidAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
    RemainingAmount DECIMAL(18,2) NOT NULL,
    IsPaid BIT NOT NULL DEFAULT 0,
    PaidDate DATETIME2,
    LateFee DECIMAL(18,2) NOT NULL DEFAULT 0,
    PaymentId INT,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY (SaleId) REFERENCES Sales(SaleId),
    FOREIGN KEY (PaymentId) REFERENCES Payments(PaymentId)
);
```

### جدول الأرشيف (Archives)
```sql
CREATE TABLE Archives (
    ArchiveId INT IDENTITY(1,1) PRIMARY KEY,
    EntityType NVARCHAR(50) NOT NULL, -- Customer, Car, Sale
    EntityId INT NOT NULL,
    FileName NVARCHAR(255) NOT NULL,
    OriginalFileName NVARCHAR(255) NOT NULL,
    FilePath NVARCHAR(500) NOT NULL,
    FileSize BIGINT NOT NULL,
    FileType NVARCHAR(50),
    Description NVARCHAR(500),
    UploadedBy INT NOT NULL,
    UploadedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    IsActive BIT NOT NULL DEFAULT 1,
    FOREIGN KEY (UploadedBy) REFERENCES Users(UserId)
);
```

### جدول إعدادات النظام (SystemSettings)
```sql
CREATE TABLE SystemSettings (
    SettingId INT IDENTITY(1,1) PRIMARY KEY,
    SettingKey NVARCHAR(100) NOT NULL UNIQUE,
    SettingValue NVARCHAR(1000),
    Description NVARCHAR(500),
    ModifiedBy INT,
    ModifiedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId)
);
```

### جدول معلومات المعرض (ShowroomInfo)
```sql
CREATE TABLE ShowroomInfo (
    ShowroomId INT IDENTITY(1,1) PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,
    Address NVARCHAR(500),
    Phone NVARCHAR(50),
    Email NVARCHAR(100),
    Website NVARCHAR(100),
    LogoPath NVARCHAR(500),
    TaxNumber NVARCHAR(50),
    CommercialRegister NVARCHAR(50),
    ModifiedBy INT,
    ModifiedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId)
);
```

### جدول تراخيص النظام (Licenses)
```sql
CREATE TABLE Licenses (
    LicenseId INT IDENTITY(1,1) PRIMARY KEY,
    LicenseKey NVARCHAR(255) NOT NULL UNIQUE,
    SubscriptionType INT NOT NULL, -- 0: Monthly, 1: Yearly, 2: Lifetime
    StartDate DATETIME2 NOT NULL,
    EndDate DATETIME2,
    IsActive BIT NOT NULL DEFAULT 1,
    MachineId NVARCHAR(255) NOT NULL,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    ActivatedDate DATETIME2,
    DeactivatedDate DATETIME2
);
```



## طبقة الوصول للبيانات (Data Access Layer - DAL)

ستكون طبقة الوصول للبيانات مسؤولة عن التفاعل مع قاعدة البيانات. سنستخدم Entity Framework Core لتبسيط عمليات CRUD (إنشاء، قراءة، تحديث، حذف) ولتوفير تجريد عن تفاصيل قاعدة البيانات الأساسية.

### أمثلة على واجهات المستودع (Repositories)

**`IUserRepository.cs`**
```csharp
using CarSalesSystem.Core.Models;

namespace CarSalesSystem.Core.Interfaces
{
    public interface IUserRepository : IGenericRepository<User>
    {
        User GetByUsername(string username);
        IEnumerable<User> GetUsersByRole(UserRole role);
        bool IsUsernameExists(string username);
    }
}
```

**`IGenericRepository.cs`**
```csharp
using System.Linq.Expressions;

namespace CarSalesSystem.Core.Interfaces
{
    public interface IGenericRepository<T>
    where T : class
    {
        T GetById(int id);
        IEnumerable<T> GetAll();
        IEnumerable<T> Find(Expression<Func<T, bool>> expression);
        void Add(T entity);
        void AddRange(IEnumerable<T> entities);
        void Update(T entity);
        void Remove(T entity);
        void RemoveRange(IEnumerable<T> entities);
    }
}
```

### أمثلة على تنفيذ المستودع (Repository Implementation)

**`UserRepository.cs`**
```csharp
using CarSalesSystem.Core.Interfaces;
using CarSalesSystem.Core.Models;
using CarSalesSystem.Data.Context;
using Microsoft.EntityFrameworkCore;

namespace CarSalesSystem.Data.Repositories
{
    public class UserRepository : GenericRepository<User>, IUserRepository
    {
        public UserRepository(CarSalesDbContext context) : base(context)
        {
        }

        public User GetByUsername(string username)
        {
            return _context.Users.FirstOrDefault(u => u.Username == username);
        }

        public IEnumerable<User> GetUsersByRole(UserRole role)
        {
            return _context.Users.Where(u => u.Role == role).ToList();
        }

        public bool IsUsernameExists(string username)
        {
            return _context.Users.Any(u => u.Username == username);
        }
    }
}
```

**`GenericRepository.cs`**
```csharp
using CarSalesSystem.Core.Interfaces;
using CarSalesSystem.Data.Context;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace CarSalesSystem.Data.Repositories
{
    public class GenericRepository<T> : IGenericRepository<T> where T : class
    {
        protected readonly CarSalesDbContext _context;

        public GenericRepository(CarSalesDbContext context)
        {
            _context = context;
        }

        public T GetById(int id)
        {
            return _context.Set<T>().Find(id);
        }

        public IEnumerable<T> GetAll()
        {
            return _context.Set<T>().ToList();
        }

        public IEnumerable<T> Find(Expression<Func<T, bool>> expression)
        {
            return _context.Set<T>().Where(expression);
        }

        public void Add(T entity)
        {
            _context.Set<T>().Add(entity);
        }

        public void AddRange(IEnumerable<T> entities)
        {
            _context.Set<T>().AddRange(entities);
        }

        public void Update(T entity)
        {
            _context.Set<T>().Update(entity);
        }

        public void Remove(T entity)
        {
            _context.Set<T>().Remove(entity);
        }

        public void RemoveRange(IEnumerable<T> entities)
        {
            _context.Set<T>().RemoveRange(entities);
        }
    }
}
```

## طبقة منطق الأعمال (Business Logic Layer - BLL)

ستحتوي طبقة منطق الأعمال على الخدمات التي تتعامل مع القواعد التجارية والتحقق من صحة البيانات. ستتفاعل هذه الخدمات مع طبقة الوصول للبيانات لتنفيذ العمليات المطلوبة.

### أمثلة على الخدمات (Services)

**`UserService.cs`**
```csharp
using CarSalesSystem.Core.Interfaces;
using CarSalesSystem.Core.Models;
using CarSalesSystem.Business.Helpers;

namespace CarSalesSystem.Business.Services
{
    public class UserService
    {
        private readonly IUserRepository _userRepository;

        public UserService(IUserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        public User Authenticate(string username, string password)
        {
            var user = _userRepository.GetByUsername(username);
            if (user == null || !EncryptionHelper.VerifyPasswordHash(password, user.PasswordHash))
            {
                return null;
            }
            user.LastLoginDate = DateTime.Now;
            _userRepository.Update(user);
            return user;
        }

        public User CreateUser(User user, string password)
        {
            if (_userRepository.IsUsernameExists(user.Username))
            {
                throw new InvalidOperationException("اسم المستخدم موجود بالفعل.");
            }
            user.PasswordHash = EncryptionHelper.HashPassword(password);
            _userRepository.Add(user);
            return user;
        }

        public void UpdateUser(User user)
        {
            _userRepository.Update(user);
        }

        public void DeleteUser(int userId)
        {
            var user = _userRepository.GetById(userId);
            if (user != null)
            {
                _userRepository.Remove(user);
            }
        }

        public IEnumerable<User> GetAllUsers()
        {
            return _userRepository.GetAll();
        }

        public User GetUserById(int userId)
        {
            return _userRepository.GetById(userId);
        }

        public IEnumerable<User> GetSalesAgents()
        {
            return _userRepository.GetUsersByRole(UserRole.SalesAgent);
        }
    }
}
```

**`EncryptionHelper.cs`**
```csharp
using System.Security.Cryptography;
using System.Text;

namespace CarSalesSystem.Business.Helpers
{
    public static class EncryptionHelper
    {
        public static string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return BitConverter.ToString(hashedBytes).Replace("-", "").ToLower();
            }
        }

        public static bool VerifyPasswordHash(string password, string storedHash)
        {
            var hashOfInput = HashPassword(password);
            StringComparer comparer = StringComparer.OrdinalIgnoreCase;
            return comparer.Compare(hashOfInput, storedHash) == 0;
        }
    }
}
```

## نظام إدارة المستخدمين والصلاحيات

سيتضمن النظام ثلاثة أدوار رئيسية:

1.  **المطور (Developer)**: يمتلك صلاحيات كاملة على النظام، بما في ذلك التحكم في الإعدادات الأساسية، تحديث البرنامج، وإدارة التراخيص. يمكن للمطور الوصول إلى وظيفة "ضبط المصنع" للبرنامج. كلمة التحكم الخاصة بالمطور هي "abrar".
2.  **المدير (Manager)**: يمتلك صلاحيات إدارة النشاط بالكامل، مثل إضافة وتعديل وحذف السيارات والعملاء والمبيعات، وإدارة المستخدمين (باستثناء المطورين)، والوصول إلى جميع التقارير. لا يمكن للمدير الوصول إلى صلاحيات المطور.
3.  **مندوب المبيعات (Sales Agent)**: يمتلك صلاحيات محدودة يحددها المدير، مثل إضافة مبيعات جديدة، عرض بيانات العملاء والسيارات، وإدارة دفعات الأقساط الخاصة بمبيعاته.

### ميزات إدارة المستخدمين:
-   **تسجيل الدخول والخروج**: نظام آمن لتسجيل الدخول باستخدام اسم المستخدم وكلمة المرور.
-   **إدارة المستخدمين**: يمكن للمدير إضافة، تعديل، تعطيل، وتعيين أدوار لمندوبي المبيعات.
-   **تغيير كلمة المرور**: يمكن لكل مستخدم تغيير كلمة مروره.
-   **تعيين الصلاحيات**: يمكن للمدير تحديد صلاحيات محددة لمندوبي المبيعات (مثلاً، هل يمكنه حذف مبيعة، هل يمكنه تعديل سعر سيارة).

### تنفيذ صلاحيات المطور:
سيتم تضمين آلية للتحقق من كلمة "abrar" لتمكين وظائف المطور الخاصة، مثل تحديث البرنامج أو الوصول إلى إعدادات النظام الحساسة. يمكن أن يتم ذلك من خلال واجهة مخفية أو قائمة خاصة تظهر فقط عند إدخال هذه الكلمة في مكان مخصص (مثلاً، في شاشة تسجيل الدخول أو في شاشة الإعدادات).

```csharp
// مثال على التحقق من صلاحيات المطور
public bool IsDeveloperModeEnabled(string secretKey)
{
    return secretKey == "abrar";
}

// في واجهة المستخدم، عند محاولة الوصول لوظيفة المطور:
// if (userService.IsDeveloperModeEnabled(inputSecretKey))
// {
//     // تمكين وظائف المطور
// }
```

## نظام المبيعات والدفع

### أنواع المبيعات:
-   **مبيعات الكاش**: يتم دفع المبلغ بالكامل عند الشراء.
-   **مبيعات التقسيط**: يتم دفع دفعة أولى، ثم يتم تقسيم المبلغ المتبقي على أقساط شهرية أو حسب الاتفاق.

### إدارة التقسيط:
-   **إنشاء جدول أقساط**: عند إتمام عملية بيع بالتقسيط، يتم إنشاء جدول أقساط تلقائيًا بناءً على المبلغ المتبقي، عدد الأقساط، وسعر الفائدة (إن وجد).
-   **تتبع الدفعات**: يتم تسجيل كل دفعة يقوم بها العميل، وتحديث حالة الأقساط المتبقية.
-   **منع حذف العميل في التقسيط**: لا يمكن حذف العميل إذا كان لديه أقساط مستحقة أو غير مكتملة. بدلاً من ذلك، يمكن تعطيل حسابه أو استبداله.
-   **استبدال العميل في التقسيط**: هذه ميزة فريدة تسمح بنقل جميع الدفعات والأقساط المستحقة من عميل إلى عميل آخر. هذا مفيد في حالات التنازل أو تغيير ملكية العقد.

```csharp
// مثال على وظيفة استبدال العميل في التقسيط
public void ReplaceCustomerInInstallment(int oldCustomerId, int newCustomerId, int saleId)
{
    var sale = _saleRepository.GetById(saleId);
    if (sale == null || sale.PaymentMethod != PaymentMethod.Installment)
    {
        throw new InvalidOperationException("عملية البيع غير موجودة أو ليست بالتقسيط.");
    }

    var oldCustomer = _customerRepository.GetById(oldCustomerId);
    var newCustomer = _customerRepository.GetById(newCustomerId);

    if (oldCustomer == null || newCustomer == null)
    {
        throw new InvalidOperationException("العميل القديم أو الجديد غير موجود.");
    }

    // التحقق من أن العميل القديم هو بالفعل العميل المرتبط بعملية البيع
    if (sale.CustomerId != oldCustomerId)
    {
        throw new InvalidOperationException("العميل القديم المحدد لا يتطابق مع العميل المرتبط بعملية البيع.");
    }

    // تحديث العميل في عملية البيع
    sale.CustomerId = newCustomerId;
    _saleRepository.Update(sale);

    // تحديث أي سجلات دفع أو أقساط مرتبطة مباشرة بالعميل (إذا كانت موجودة، على الرغم من أنها مرتبطة بالبيع)
    // في هذا التصميم، الأقساط والدفعات مرتبطة بـ SaleId، لذا تغيير CustomerId في Sale يكفي.
    // ومع ذلك، إذا كانت هناك سجلات أخرى مرتبطة مباشرة بـ CustomerId في سياق التقسيط، يجب تحديثها هنا.

    // يمكن إضافة سجل تدقيق هنا لتتبع عملية الاستبدال
    Console.WriteLine($"تم استبدال العميل {oldCustomer.FullName} بـ {newCustomer.FullName} في عملية البيع رقم {sale.SaleNumber}.");
}
```

## نظام الطباعة والتقارير

سيتم استخدام تقنيات مثل Crystal Reports أو ReportViewer لإنشاء تقارير احترافية.

### تقارير رئيسية:
-   **كشف حساب العميل**: تقرير مفصل يوضح جميع عمليات الشراء (كاش وتقسيط)، الدفعات التي تمت، والأقساط المستحقة أو المتأخرة للعميل.
-   **تقرير المبيعات**: ملخص للمبيعات خلال فترة زمنية محددة، مع إمكانية التصفية حسب نوع السيارة، طريقة الدفع، أو مندوب المبيعات.
-   **تقرير الأقساط المستحقة**: قائمة بجميع الأقساط التي حان موعد استحقاقها أو تأخرت.
-   **تقرير جرد السيارات**: قائمة بالسيارات المتوفرة للبيع، مع تفاصيلها وحالتها.

### تخصيص التقارير:
-   **شعار المعرض وبياناته**: سيتم تضمين شعار المعرض واسمه وعنوانه وأرقام الاتصال به في رأس كل تقرير. سيتم سحب هذه البيانات من جدول `ShowroomInfo`.
-   **تنسيق احترافي**: تصميم التقارير سيكون نظيفًا واحترافيًا، مع استخدام خطوط وألوان متناسقة.

## نظام النسخ الاحتياطية والأرشفة

### النسخ الاحتياطية لقاعدة البيانات:
-   **نسخ احتياطي يدوي**: يمكن للمدير أو المطور إنشاء نسخة احتياطية كاملة لقاعدة البيانات في أي وقت.
-   **نسخ احتياطي تلقائي**: يمكن جدولة نسخ احتياطية تلقائية (يومية/أسبوعية) إلى مسار محدد.
-   **استرجاع النسخ الاحتياطية**: واجهة سهلة لاستعراض النسخ الاحتياطية المتاحة واسترجاع أي منها. يجب أن يتطلب هذا صلاحيات المطور أو المدير.

```csharp
// مثال على وظيفة النسخ الاحتياطي لقاعدة البيانات (SQL Server)
public void BackupDatabase(string backupPath, string databaseName, string serverName)
{
    string connectionString = $"Data Source={serverName};Initial Catalog=master;Integrated Security=True";
    using (SqlConnection connection = new SqlConnection(connectionString))
    {
        connection.Open();
        string query = $"BACKUP DATABASE [{databaseName}] TO DISK = N'{backupPath}\\ {databaseName}-{DateTime.Now.ToString("yyyyMMddHHmmss")}.bak' WITH NOFORMAT, NOINIT, NAME = N'{databaseName}-Full Database Backup', SKIP, NOREWIND, NOUNLOAD, STATS = 10";
        using (SqlCommand command = new SqlCommand(query, connection))
        {
            command.ExecuteNonQuery();
        }
    }
    Console.WriteLine("تم إنشاء النسخة الاحتياطية بنجاح.");
}

// مثال على وظيفة استرجاع قاعدة البيانات (SQL Server)
public void RestoreDatabase(string backupFilePath, string databaseName, string serverName)
{
    string connectionString = $"Data Source={serverName};Initial Catalog=master;Integrated Security=True";
    using (SqlConnection connection = new SqlConnection(connectionString))
    {
        connection.Open();
        // قطع الاتصالات النشطة بقاعدة البيانات
        string killConnectionsQuery = $"ALTER DATABASE [{databaseName}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE; USE master;";
        using (SqlCommand command = new SqlCommand(killConnectionsQuery, connection))
        {
            command.ExecuteNonQuery();
        }

        string restoreQuery = $"RESTORE DATABASE [{databaseName}] FROM DISK = N'{backupFilePath}' WITH FILE = 1, NOUNLOAD, REPLACE, STATS = 10;";
        using (SqlCommand command = new SqlCommand(restoreQuery, connection))
        {
            command.ExecuteNonQuery();
        }

        // إعادة قاعدة البيانات إلى وضع الاتصال المتعدد
        string multiUserQuery = $"ALTER DATABASE [{databaseName}] SET MULTI_USER;";
        using (SqlCommand command = new SqlCommand(multiUserQuery, connection))
        {
            command.ExecuteNonQuery();
        }
    }
    Console.WriteLine("تم استعادة قاعدة البيانات بنجاح.");
}
```

### نظام الأرشفة الاحترافي:
-   **تخزين ملفات السيارة والعملاء**: سيتمكن المستخدمون من رفع وتخزين المستندات المتعلقة بالسيارات (مثل عقود الشراء، رخص السيارة، صور السيارة) والعملاء (مثل الهوية، عقود البيع، مستندات الدخل).
-   **تنظيم الملفات**: سيتم تنظيم الملفات في مجلدات مخصصة لكل سيارة أو عميل، مع إمكانية إضافة وصف لكل ملف.
-   **عرض الملفات المؤرشفة**: واجهة سهلة لعرض الملفات المؤرشفة، مع إمكانية البحث والتصفية حسب نوع الملف، السيارة، أو العميل. يمكن فتح الملفات مباشرة من داخل النظام.
-   **التشفير**: يمكن إضافة خيار لتشفير الملفات الحساسة المخزنة لزيادة الأمان.

## نظام الحماية والترخيص

### منع تنشيط البرنامج على جهاز آخر:
-   **تفعيل الاشتراك**: عند شراء الاشتراك (سنوي، شهري، دائم)، سيتم ربط ترخيص البرنامج بجهاز واحد فقط (Machine ID). يمكن أن يكون Machine ID عبارة عن مزيج من معلومات الجهاز الفريدة (مثل MAC Address، CPU ID، HDD Serial Number).
-   **التحقق عند التشغيل**: عند كل تشغيل للبرنامج، سيتم التحقق من تطابق Machine ID الحالي مع Machine ID المسجل في الترخيص. إذا لم يتطابق، لن يتم تنشيط البرنامج.
-   **إلغاء التنشيط**: يجب أن تكون هناك آلية لإلغاء تنشيط البرنامج من جهاز معين للسماح بتنشيطه على جهاز آخر (مثلاً، عند تغيير الجهاز أو إعادة تثبيت النظام). هذه العملية تتطلب صلاحيات المطور أو التواصل مع الدعم الفني.

```csharp
// مثال على الحصول على Machine ID (مثال مبسط، يتطلب تنفيذ أكثر تعقيدًا في الواقع)
public string GetMachineId()
{
    // يجب استخدام مزيج من معرفات الجهاز الفريدة لإنشاء Machine ID قوي
    // مثل MAC Address، CPU ID، Volume Serial Number
    string machineId = Environment.MachineName + "-" + Environment.ProcessorCount.ToString();
    // يمكن استخدام WMI للحصول على معلومات أكثر تفصيلاً وفريدة
    return EncryptionHelper.HashPassword(machineId); // تشفير المعرف لزيادة الأمان
}

// مثال على التحقق من الترخيص
public bool IsLicenseValid(string licenseKey)
{
    var license = _licenseRepository.GetByLicenseKey(licenseKey);
    if (license == null || !license.IsActive)
    {
        return false;
    }

    if (license.MachineId != GetMachineId())
    {
        return false; // البرنامج ليس مرخصًا لهذا الجهاز
    }

    if (license.SubscriptionType != SubscriptionType.Lifetime && license.EndDate < DateTime.Now)
    {
        return false; // انتهت صلاحية الاشتراك
    }

    return true;
}
```

### نظام الاشتراك:
-   **أنواع الاشتراك**: سنوي، شهري، دائم.
-   **إدارة الاشتراكات**: يمكن للمطور أو المدير (بصلاحيات محددة) إدارة الاشتراكات، مثل تفعيل اشتراك جديد، تمديد اشتراك، أو إلغاء اشتراك.
-   **تنبيهات انتهاء الصلاحية**: يمكن للنظام إرسال تنبيهات للمستخدمين قبل انتهاء صلاحية اشتراكهم بفترة كافية.

## واجهة المستخدم (User Interface - UI)

سيتم تصميم واجهة المستخدم لتكون سهلة الاستخدام، بديهية، وفعالة. يمكن استخدام Windows Forms أو WPF لتطوير الواجهة.

### المكونات الرئيسية للواجهة:
-   **شاشة تسجيل الدخول**: للسماح للمستخدمين بالوصول إلى النظام.
-   **الشاشة الرئيسية (Dashboard)**: تعرض ملخصًا سريعًا للمبيعات، الأقساط المستحقة، والسيارات المتوفرة.
-   **إدارة السيارات**: شاشات لإضافة، تعديل، عرض، والبحث عن السيارات.
-   **إدارة العملاء**: شاشات لإضافة، تعديل، عرض، والبحث عن العملاء.
-   **إدارة المبيعات**: شاشات لإنشاء مبيعات جديدة (كاش أو تقسيط)، وعرض تفاصيل المبيعات.
-   **إدارة الدفعات والأقساط**: شاشات لتسجيل الدفعات، وتتبع حالة الأقساط.
-   **التقارير**: واجهة لعرض وطباعة التقارير المختلفة.
-   **النسخ الاحتياطية والأرشفة**: واجهات لإدارة النسخ الاحتياطية والملفات المؤرشفة.
-   **إدارة المستخدمين والصلاحيات**: واجهة للمدير لإدارة حسابات المستخدمين وتعيين صلاحياتهم.
-   **إعدادات النظام**: واجهة للمطور لإدارة إعدادات النظام ومعلومات المعرض ونظام الترخيص.

### تصميم الواجهة:
-   **تصميم متجاوب**: قدر الإمكان، يجب أن تكون الواجهة متجاوبة مع أحجام الشاشات المختلفة.
-   **تجربة المستخدم (UX)**: التركيز على سهولة الاستخدام وتقليل عدد النقرات لإنجاز المهام.
-   **التحقق من صحة الإدخال**: التحقق من صحة البيانات المدخلة من قبل المستخدم لمنع الأخطاء.

## توثيق المشروع وتسليم الكود الكامل

سيتم توفير توثيق شامل للمشروع يتضمن:
-   **دليل المستخدم**: يشرح كيفية استخدام البرنامج لجميع الأدوار (مدير، مندوب مبيعات).
-   **دليل المطور**: يشرح بنية الكود، تصميم قاعدة البيانات، وكيفية تجميع وتشغيل المشروع.
-   **متطلبات النظام**: قائمة بالمتطلبات اللازمة لتشغيل البرنامج (نظام التشغيل، .NET Runtime، SQL Server).
-   **الكود المصدري**: سيتم تسليم الكود المصدري الكامل للمشروع، مع تعليقات واضحة ومنظمة.

## خلاصة

يهدف هذا البرمبت إلى توفير خارطة طريق مفصلة لتطوير نظام إدارة مبيعات السيارات. من خلال اتباع هذه المتطلبات والتصميم المقترح، يمكن بناء نظام قوي، مرن، وآمن يلبي احتياجات المعرض بشكل فعال.



## تصميم كشف حساب العميل مع اللوجو

### تخطيط كشف حساب العميل

سيتم تصميم كشف حساب العميل ليتضمن العناصر التالية بترتيب احترافي:

#### 1. رأس التقرير (Header)
- **لوجو المعرض**: سيتم وضع لوجو المعرض في الزاوية اليمنى العلوية من التقرير
- **اسم المعرض**: بخط كبير وواضح
- **عنوان المعرض**: العنوان الكامل مع المدينة
- **أرقام الهاتف**: أرقام الاتصال الرئيسية
- **البريد الإلكتروني والموقع**: إن وجد
- **رقم السجل التجاري**: رقم السجل التجاري للمعرض
- **الرقم الضريبي**: الرقم الضريبي للمعرض

#### 2. معلومات العميل
- **اسم العميل**: الاسم الكامل
- **رقم العميل**: الرقم المرجعي للعميل
- **رقم الهوية**: رقم الهوية الوطنية
- **رقم الهاتف**: أرقام الاتصال
- **العنوان**: عنوان العميل الكامل

#### 3. تفاصيل المبيعات والدفعات
- **جدول المبيعات**: يعرض جميع السيارات المشتراة
- **جدول الدفعات**: يعرض جميع الدفعات المسددة
- **جدول الأقساط**: يعرض الأقساط المستحقة والمتبقية

#### 4. ملخص الحساب
- **إجمالي المبيعات**: المبلغ الإجمالي لجميع المشتريات
- **إجمالي المدفوع**: المبلغ الإجمالي المسدد
- **الرصيد المتبقي**: المبلغ المتبقي على العميل

### تنفيذ عرض اللوجو في التقرير

```csharp
// خدمة إدارة اللوجو والتقارير
public class ReportService
{
    private readonly IShowroomRepository _showroomRepository;
    private readonly ICustomerRepository _customerRepository;
    private readonly ISaleRepository _saleRepository;
    private readonly IPaymentRepository _paymentRepository;

    public ReportService(IShowroomRepository showroomRepository, 
                        ICustomerRepository customerRepository,
                        ISaleRepository saleRepository,
                        IPaymentRepository paymentRepository)
    {
        _showroomRepository = showroomRepository;
        _customerRepository = customerRepository;
        _saleRepository = saleRepository;
        _paymentRepository = paymentRepository;
    }

    public void GenerateCustomerStatement(int customerId, string outputPath)
    {
        var customer = _customerRepository.GetById(customerId);
        var showroomInfo = _showroomRepository.GetShowroomInfo();
        var sales = _saleRepository.GetSalesByCustomerId(customerId);
        var payments = _paymentRepository.GetPaymentsByCustomerId(customerId);

        // إنشاء مستند PDF
        using (var document = new Document(PageSize.A4))
        {
            var writer = PdfWriter.GetInstance(document, new FileStream(outputPath, FileMode.Create));
            document.Open();

            // إضافة اللوجو ومعلومات المعرض
            AddHeaderWithLogo(document, showroomInfo);
            
            // إضافة معلومات العميل
            AddCustomerInfo(document, customer);
            
            // إضافة تفاصيل المبيعات
            AddSalesDetails(document, sales);
            
            // إضافة تفاصيل الدفعات
            AddPaymentDetails(document, payments);
            
            // إضافة ملخص الحساب
            AddAccountSummary(document, sales, payments);

            document.Close();
        }
    }

    private void AddHeaderWithLogo(Document document, ShowroomInfo showroomInfo)
    {
        // إنشاء جدول للرأس
        var headerTable = new PdfPTable(2);
        headerTable.WidthPercentage = 100;
        headerTable.SetWidths(new float[] { 30f, 70f });

        // إضافة اللوجو
        if (!string.IsNullOrEmpty(showroomInfo.LogoPath) && File.Exists(showroomInfo.LogoPath))
        {
            var logo = Image.GetInstance(showroomInfo.LogoPath);
            logo.ScaleToFit(100f, 80f);
            logo.Alignment = Element.ALIGN_CENTER;
            
            var logoCell = new PdfPCell(logo);
            logoCell.Border = Rectangle.NO_BORDER;
            logoCell.HorizontalAlignment = Element.ALIGN_CENTER;
            logoCell.VerticalAlignment = Element.ALIGN_MIDDLE;
            headerTable.AddCell(logoCell);
        }
        else
        {
            // إذا لم يكن هناك لوجو، إضافة خلية فارغة
            var emptyCell = new PdfPCell(new Phrase(""));
            emptyCell.Border = Rectangle.NO_BORDER;
            headerTable.AddCell(emptyCell);
        }

        // إضافة معلومات المعرض
        var showroomInfoCell = new PdfPCell();
        showroomInfoCell.Border = Rectangle.NO_BORDER;
        showroomInfoCell.HorizontalAlignment = Element.ALIGN_RIGHT;

        // اسم المعرض
        var showroomNameFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 18, BaseColor.DARK_GRAY);
        var showroomNameParagraph = new Paragraph(showroomInfo.Name, showroomNameFont);
        showroomNameParagraph.Alignment = Element.ALIGN_RIGHT;
        showroomInfoCell.AddElement(showroomNameParagraph);

        // عنوان المعرض
        if (!string.IsNullOrEmpty(showroomInfo.Address))
        {
            var addressFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, BaseColor.BLACK);
            var addressParagraph = new Paragraph(showroomInfo.Address, addressFont);
            addressParagraph.Alignment = Element.ALIGN_RIGHT;
            showroomInfoCell.AddElement(addressParagraph);
        }

        // رقم الهاتف
        if (!string.IsNullOrEmpty(showroomInfo.Phone))
        {
            var phoneFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, BaseColor.BLACK);
            var phoneParagraph = new Paragraph($"هاتف: {showroomInfo.Phone}", phoneFont);
            phoneParagraph.Alignment = Element.ALIGN_RIGHT;
            showroomInfoCell.AddElement(phoneParagraph);
        }

        // البريد الإلكتروني
        if (!string.IsNullOrEmpty(showroomInfo.Email))
        {
            var emailFont = FontFactory.GetFont(FontFactory.HELVETICA, 12, BaseColor.BLACK);
            var emailParagraph = new Paragraph($"بريد إلكتروني: {showroomInfo.Email}", emailFont);
            emailParagraph.Alignment = Element.ALIGN_RIGHT;
            showroomInfoCell.AddElement(emailParagraph);
        }

        // رقم السجل التجاري
        if (!string.IsNullOrEmpty(showroomInfo.CommercialRegister))
        {
            var crFont = FontFactory.GetFont(FontFactory.HELVETICA, 10, BaseColor.GRAY);
            var crParagraph = new Paragraph($"س.ت: {showroomInfo.CommercialRegister}", crFont);
            crParagraph.Alignment = Element.ALIGN_RIGHT;
            showroomInfoCell.AddElement(crParagraph);
        }

        // الرقم الضريبي
        if (!string.IsNullOrEmpty(showroomInfo.TaxNumber))
        {
            var taxFont = FontFactory.GetFont(FontFactory.HELVETICA, 10, BaseColor.GRAY);
            var taxParagraph = new Paragraph($"ر.ض: {showroomInfo.TaxNumber}", taxFont);
            taxParagraph.Alignment = Element.ALIGN_RIGHT;
            showroomInfoCell.AddElement(taxParagraph);
        }

        headerTable.AddCell(showroomInfoCell);
        document.Add(headerTable);

        // إضافة خط فاصل
        var line = new LineSeparator(1f, 100f, BaseColor.GRAY, Element.ALIGN_CENTER, -1);
        document.Add(new Chunk(line));
        document.Add(new Paragraph(" ")); // مسافة فارغة
    }

    private void AddCustomerInfo(Document document, Customer customer)
    {
        // عنوان القسم
        var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 16, BaseColor.DARK_GRAY);
        var title = new Paragraph("كشف حساب العميل", titleFont);
        title.Alignment = Element.ALIGN_CENTER;
        document.Add(title);
        document.Add(new Paragraph(" ")); // مسافة فارغة

        // جدول معلومات العميل
        var customerTable = new PdfPTable(4);
        customerTable.WidthPercentage = 100;
        customerTable.SetWidths(new float[] { 25f, 25f, 25f, 25f });

        // رأس الجدول
        var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 12, BaseColor.WHITE);
        var headerBackgroundColor = new BaseColor(70, 130, 180); // Steel Blue

        AddTableCell(customerTable, "اسم العميل", headerFont, headerBackgroundColor);
        AddTableCell(customerTable, "رقم العميل", headerFont, headerBackgroundColor);
        AddTableCell(customerTable, "رقم الهوية", headerFont, headerBackgroundColor);
        AddTableCell(customerTable, "رقم الهاتف", headerFont, headerBackgroundColor);

        // بيانات العميل
        var dataFont = FontFactory.GetFont(FontFactory.HELVETICA, 11, BaseColor.BLACK);
        AddTableCell(customerTable, customer.FullName, dataFont, BaseColor.WHITE);
        AddTableCell(customerTable, customer.CustomerCode, dataFont, BaseColor.WHITE);
        AddTableCell(customerTable, customer.NationalId ?? "غير محدد", dataFont, BaseColor.WHITE);
        AddTableCell(customerTable, customer.Phone, dataFont, BaseColor.WHITE);

        document.Add(customerTable);
        document.Add(new Paragraph(" ")); // مسافة فارغة
    }

    private void AddTableCell(PdfPTable table, string text, Font font, BaseColor backgroundColor)
    {
        var cell = new PdfPCell(new Phrase(text, font));
        cell.BackgroundColor = backgroundColor;
        cell.HorizontalAlignment = Element.ALIGN_CENTER;
        cell.VerticalAlignment = Element.ALIGN_MIDDLE;
        cell.Padding = 8f;
        table.AddCell(cell);
    }

    // باقي الوظائف لإضافة تفاصيل المبيعات والدفعات...
}
```

### إعدادات اللوجو في النظام

```csharp
// نموذج معلومات المعرض
public class ShowroomInfo
{
    public int ShowroomId { get; set; }
    public string Name { get; set; }
    public string Address { get; set; }
    public string Phone { get; set; }
    public string Email { get; set; }
    public string Website { get; set; }
    public string LogoPath { get; set; } // مسار ملف اللوجو
    public string TaxNumber { get; set; }
    public string CommercialRegister { get; set; }
    public int? ModifiedBy { get; set; }
    public DateTime ModifiedDate { get; set; }
}

// خدمة إدارة إعدادات المعرض
public class ShowroomService
{
    private readonly IShowroomRepository _showroomRepository;

    public ShowroomService(IShowroomRepository showroomRepository)
    {
        _showroomRepository = showroomRepository;
    }

    public void UpdateShowroomLogo(string logoFilePath)
    {
        // التحقق من صحة الملف
        if (!File.Exists(logoFilePath))
        {
            throw new FileNotFoundException("ملف اللوجو غير موجود.");
        }

        // التحقق من نوع الملف (صورة)
        var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".bmp", ".gif" };
        var fileExtension = Path.GetExtension(logoFilePath).ToLower();
        
        if (!allowedExtensions.Contains(fileExtension))
        {
            throw new InvalidOperationException("نوع الملف غير مدعوم. يرجى اختيار ملف صورة صحيح.");
        }

        // نسخ الملف إلى مجلد الموارد
        var resourcesPath = Path.Combine(Application.StartupPath, "Resources", "Images");
        if (!Directory.Exists(resourcesPath))
        {
            Directory.CreateDirectory(resourcesPath);
        }

        var newLogoPath = Path.Combine(resourcesPath, "showroom_logo" + fileExtension);
        File.Copy(logoFilePath, newLogoPath, true);

        // تحديث مسار اللوجو في قاعدة البيانات
        var showroomInfo = _showroomRepository.GetShowroomInfo();
        if (showroomInfo == null)
        {
            showroomInfo = new ShowroomInfo();
        }
        
        showroomInfo.LogoPath = newLogoPath;
        showroomInfo.ModifiedDate = DateTime.Now;
        
        _showroomRepository.UpdateShowroomInfo(showroomInfo);
    }

    public ShowroomInfo GetShowroomInfo()
    {
        return _showroomRepository.GetShowroomInfo();
    }

    public void UpdateShowroomInfo(ShowroomInfo showroomInfo)
    {
        showroomInfo.ModifiedDate = DateTime.Now;
        _showroomRepository.UpdateShowroomInfo(showroomInfo);
    }
}
```

