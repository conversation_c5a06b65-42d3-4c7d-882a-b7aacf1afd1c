using CarSalesSystem.Core.Models;

namespace CarSalesSystem.Core.Interfaces
{
    /// <summary>
    /// واجهة مستودع الأقساط
    /// </summary>
    public interface IInstallmentRepository : IGenericRepository<Installment>
    {
        /// <summary>
        /// الحصول على أقساط المبيعة
        /// </summary>
        /// <param name="saleId">معرف المبيعة</param>
        /// <returns>قائمة الأقساط</returns>
        IEnumerable<Installment> GetInstallmentsBySaleId(int saleId);

        /// <summary>
        /// الحصول على الأقساط المستحقة
        /// </summary>
        /// <returns>قائمة الأقساط المستحقة</returns>
        IEnumerable<Installment> GetDueInstallments();

        /// <summary>
        /// الحصول على الأقساط المتأخرة
        /// </summary>
        /// <returns>قائمة الأقساط المتأخرة</returns>
        IEnumerable<Installment> GetOverdueInstallments();

        /// <summary>
        /// الحصول على الأقساط المدفوعة
        /// </summary>
        /// <param name="saleId">معرف المبيعة</param>
        /// <returns>قائمة الأقساط المدفوعة</returns>
        IEnumerable<Installment> GetPaidInstallments(int saleId);

        /// <summary>
        /// الحصول على الأقساط غير المدفوعة
        /// </summary>
        /// <param name="saleId">معرف المبيعة</param>
        /// <returns>قائمة الأقساط غير المدفوعة</returns>
        IEnumerable<Installment> GetUnpaidInstallments(int saleId);

        /// <summary>
        /// الحصول على الأقساط المستحقة لعميل
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <returns>قائمة الأقساط</returns>
        IEnumerable<Installment> GetDueInstallmentsByCustomer(int customerId);

        /// <summary>
        /// الحصول على الأقساط في فترة زمنية
        /// </summary>
        /// <param name="startDate">تاريخ البداية</param>
        /// <param name="endDate">تاريخ النهاية</param>
        /// <returns>قائمة الأقساط</returns>
        IEnumerable<Installment> GetInstallmentsByDateRange(DateTime startDate, DateTime endDate);

        /// <summary>
        /// تحديث حالة القسط إلى مدفوع
        /// </summary>
        /// <param name="installmentId">معرف القسط</param>
        /// <param name="paymentId">معرف الدفعة</param>
        /// <param name="paidAmount">المبلغ المدفوع</param>
        void MarkAsPaid(int installmentId, int paymentId, decimal paidAmount);

        /// <summary>
        /// إنشاء جدول أقساط لمبيعة
        /// </summary>
        /// <param name="saleId">معرف المبيعة</param>
        /// <param name="totalAmount">المبلغ الإجمالي</param>
        /// <param name="monthlyInstallment">القسط الشهري</param>
        /// <param name="numberOfMonths">عدد الأشهر</param>
        /// <param name="startDate">تاريخ البداية</param>
        void CreateInstallmentSchedule(int saleId, decimal totalAmount, decimal monthlyInstallment, int numberOfMonths, DateTime startDate);

        /// <summary>
        /// الحصول على إجمالي الأقساط المتبقية لمبيعة
        /// </summary>
        /// <param name="saleId">معرف المبيعة</param>
        /// <returns>إجمالي الأقساط المتبقية</returns>
        decimal GetRemainingInstallmentsAmount(int saleId);
    }
}
