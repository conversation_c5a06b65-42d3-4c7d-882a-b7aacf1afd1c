using CarSalesSystem.Core.Enums;
using System.ComponentModel.DataAnnotations;

namespace CarSalesSystem.Core.Models
{
    /// <summary>
    /// نموذج المبيعة
    /// </summary>
    public class Sale
    {
        [Key]
        public int SaleId { get; set; }

        [Required]
        [StringLength(20)]
        public string SaleNumber { get; set; } = string.Empty;

        [Required]
        public int CustomerId { get; set; }

        [Required]
        public int CarId { get; set; }

        public DateTime SaleDate { get; set; } = DateTime.Now;

        [Required]
        public decimal TotalAmount { get; set; }

        public decimal DownPayment { get; set; } = 0;

        [Required]
        public decimal RemainingAmount { get; set; }

        [Required]
        public PaymentMethod PaymentMethod { get; set; }

        public int? InstallmentMonths { get; set; }

        public decimal? MonthlyInstallment { get; set; }

        public decimal? InterestRate { get; set; }

        public bool IsCompleted { get; set; } = false;

        [Required]
        public int SalesAgentId { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [StringLength(1000)]
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual Customer Customer { get; set; } = null!;
        public virtual Car Car { get; set; } = null!;
        public virtual User SalesAgent { get; set; } = null!;
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
        public virtual ICollection<Installment> Installments { get; set; } = new List<Installment>();
    }
}
