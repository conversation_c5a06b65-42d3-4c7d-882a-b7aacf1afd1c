using CarSalesSystem.Core.Interfaces;
using CarSalesSystem.Core.Models;
using CarSalesSystem.Data.Context;
using Microsoft.EntityFrameworkCore;

namespace CarSalesSystem.Data.Repositories
{
    /// <summary>
    /// تنفيذ مستودع العملاء
    /// </summary>
    public class CustomerRepository : GenericRepository<Customer>, ICustomerRepository
    {
        public CustomerRepository(CarSalesDbContext context) : base(context)
        {
        }

        public Customer? GetByCustomerCode(string customerCode)
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .FirstOrDefault(c => c.CustomerCode == customerCode);
        }

        public Customer? GetByNationalId(string nationalId)
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .FirstOrDefault(c => c.NationalId == nationalId);
        }

        public IEnumerable<Customer> SearchCustomers(string searchTerm)
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .Where(c => c.FullName.Contains(searchTerm) ||
                                   c.Phone.Contains(searchTerm) ||
                                   c.CustomerCode.Contains(searchTerm) ||
                                   (c.NationalId != null && c.NationalId.Contains(searchTerm)))
                         .ToList();
        }

        public IEnumerable<Customer> GetActiveCustomers()
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .Where(c => c.IsActive)
                         .ToList();
        }

        public IEnumerable<Customer> GetCustomersWithDueInstallments()
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .Include(c => c.Sales)
                         .ThenInclude(s => s.Installments)
                         .Where(c => c.Sales.Any(s => s.Installments.Any(i => !i.IsPaid && i.DueDate <= DateTime.Now)))
                         .ToList();
        }

        public bool CanDeleteCustomer(int customerId)
        {
            var customer = _dbSet.Include(c => c.Sales)
                                 .ThenInclude(s => s.Installments)
                                 .FirstOrDefault(c => c.CustomerId == customerId);

            if (customer == null) return false;

            // لا يمكن حذف العميل إذا كان لديه مبيعات بالتقسيط غير مكتملة
            return !customer.Sales.Any(s => s.PaymentMethod == Core.Enums.PaymentMethod.Installment && 
                                           s.Installments.Any(i => !i.IsPaid));
        }

        public string GenerateCustomerCode()
        {
            var lastCustomer = _dbSet.OrderByDescending(c => c.CustomerId).FirstOrDefault();
            var nextId = (lastCustomer?.CustomerId ?? 0) + 1;
            return $"C{nextId:D6}"; // مثال: C000001
        }

        public override IEnumerable<Customer> GetAll()
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .ToList();
        }

        public override Customer? GetById(int id)
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .Include(c => c.Sales)
                         .FirstOrDefault(c => c.CustomerId == id);
        }
    }
}
