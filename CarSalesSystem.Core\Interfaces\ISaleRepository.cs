using CarSalesSystem.Core.Models;
using CarSalesSystem.Core.Enums;

namespace CarSalesSystem.Core.Interfaces
{
    /// <summary>
    /// واجهة مستودع المبيعات
    /// </summary>
    public interface ISaleRepository : IGenericRepository<Sale>
    {
        /// <summary>
        /// الحصول على مبيعة بواسطة رقم المبيعة
        /// </summary>
        /// <param name="saleNumber">رقم المبيعة</param>
        /// <returns>المبيعة أو null</returns>
        Sale? GetBySaleNumber(string saleNumber);

        /// <summary>
        /// الحصول على مبيعات العميل
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <returns>قائمة المبيعات</returns>
        IEnumerable<Sale> GetSalesByCustomerId(int customerId);

        /// <summary>
        /// الحصول على مبيعات السيارة
        /// </summary>
        /// <param name="carId">معرف السيارة</param>
        /// <returns>قائمة المبيعات</returns>
        IEnumerable<Sale> GetSalesByCarId(int carId);

        /// <summary>
        /// الحصول على مبيعات مندوب المبيعات
        /// </summary>
        /// <param name="salesAgentId">معرف مندوب المبيعات</param>
        /// <returns>قائمة المبيعات</returns>
        IEnumerable<Sale> GetSalesBySalesAgent(int salesAgentId);

        /// <summary>
        /// الحصول على المبيعات حسب طريقة الدفع
        /// </summary>
        /// <param name="paymentMethod">طريقة الدفع</param>
        /// <returns>قائمة المبيعات</returns>
        IEnumerable<Sale> GetSalesByPaymentMethod(PaymentMethod paymentMethod);

        /// <summary>
        /// الحصول على المبيعات في فترة زمنية
        /// </summary>
        /// <param name="startDate">تاريخ البداية</param>
        /// <param name="endDate">تاريخ النهاية</param>
        /// <returns>قائمة المبيعات</returns>
        IEnumerable<Sale> GetSalesByDateRange(DateTime startDate, DateTime endDate);

        /// <summary>
        /// الحصول على المبيعات المكتملة
        /// </summary>
        /// <returns>قائمة المبيعات المكتملة</returns>
        IEnumerable<Sale> GetCompletedSales();

        /// <summary>
        /// الحصول على المبيعات غير المكتملة
        /// </summary>
        /// <returns>قائمة المبيعات غير المكتملة</returns>
        IEnumerable<Sale> GetIncompleteSales();

        /// <summary>
        /// إنشاء رقم مبيعة جديد
        /// </summary>
        /// <returns>رقم المبيعة الجديد</returns>
        string GenerateSaleNumber();

        /// <summary>
        /// الحصول على إجمالي المبيعات في فترة
        /// </summary>
        /// <param name="startDate">تاريخ البداية</param>
        /// <param name="endDate">تاريخ النهاية</param>
        /// <returns>إجمالي المبيعات</returns>
        decimal GetTotalSalesAmount(DateTime startDate, DateTime endDate);
    }
}
