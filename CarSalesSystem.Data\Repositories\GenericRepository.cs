using CarSalesSystem.Core.Interfaces;
using CarSalesSystem.Data.Context;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace CarSalesSystem.Data.Repositories
{
    /// <summary>
    /// تنفيذ المستودع العام
    /// </summary>
    /// <typeparam name="T">نوع الكيان</typeparam>
    public class GenericRepository<T> : IGenericRepository<T> where T : class
    {
        protected readonly CarSalesDbContext _context;
        protected readonly DbSet<T> _dbSet;

        public GenericRepository(CarSalesDbContext context)
        {
            _context = context;
            _dbSet = context.Set<T>();
        }

        public virtual T? GetById(int id)
        {
            return _dbSet.Find(id);
        }

        public virtual IEnumerable<T> GetAll()
        {
            return _dbSet.ToList();
        }

        public virtual IEnumerable<T> Find(Expression<Func<T, bool>> expression)
        {
            return _dbSet.Where(expression).ToList();
        }

        public virtual void Add(T entity)
        {
            _dbSet.Add(entity);
        }

        public virtual void AddRange(IEnumerable<T> entities)
        {
            _dbSet.AddRange(entities);
        }

        public virtual void Update(T entity)
        {
            _dbSet.Update(entity);
        }

        public virtual void Remove(T entity)
        {
            _dbSet.Remove(entity);
        }

        public virtual void RemoveRange(IEnumerable<T> entities)
        {
            _dbSet.RemoveRange(entities);
        }

        public virtual int SaveChanges()
        {
            return _context.SaveChanges();
        }

        public virtual async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }
    }
}
