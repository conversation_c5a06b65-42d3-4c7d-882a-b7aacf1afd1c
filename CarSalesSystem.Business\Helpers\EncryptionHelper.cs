using System.Security.Cryptography;
using System.Text;

namespace CarSalesSystem.Business.Helpers
{
    /// <summary>
    /// مساعد التشفير
    /// </summary>
    public static class EncryptionHelper
    {
        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <returns>كلمة المرور المشفرة</returns>
        public static string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return BitConverter.ToString(hashedBytes).Replace("-", "").ToLower();
            }
        }

        /// <summary>
        /// التحقق من كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <param name="storedHash">الهاش المحفوظ</param>
        /// <returns>true إذا كانت صحيحة</returns>
        public static bool VerifyPasswordHash(string password, string storedHash)
        {
            var hashOfInput = HashPassword(password);
            StringComparer comparer = StringComparer.OrdinalIgnoreCase;
            return comparer.Compare(hashOfInput, storedHash) == 0;
        }

        /// <summary>
        /// إنشاء مفتاح ترخيص عشوائي
        /// </summary>
        /// <returns>مفتاح الترخيص</returns>
        public static string GenerateLicenseKey()
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var random = new Random();
            var result = new StringBuilder();

            for (int i = 0; i < 4; i++)
            {
                if (i > 0) result.Append("-");
                for (int j = 0; j < 4; j++)
                {
                    result.Append(chars[random.Next(chars.Length)]);
                }
            }

            return result.ToString(); // مثال: ABCD-1234-EFGH-5678
        }

        /// <summary>
        /// الحصول على معرف الجهاز الفريد
        /// </summary>
        /// <returns>معرف الجهاز</returns>
        public static string GetMachineId()
        {
            try
            {
                var machineInfo = new StringBuilder();
                
                // اسم الجهاز
                machineInfo.Append(Environment.MachineName);
                
                // معرف المعالج
                machineInfo.Append(Environment.ProcessorCount.ToString());
                
                // نظام التشغيل
                machineInfo.Append(Environment.OSVersion.ToString());

                // تشفير المعلومات لإنشاء معرف فريد
                return HashPassword(machineInfo.ToString());
            }
            catch
            {
                // في حالة الفشل، استخدم معرف افتراضي
                return HashPassword(Environment.MachineName + DateTime.Now.Ticks.ToString());
            }
        }

        /// <summary>
        /// تشفير نص
        /// </summary>
        /// <param name="plainText">النص الأصلي</param>
        /// <param name="key">مفتاح التشفير</param>
        /// <returns>النص المشفر</returns>
        public static string EncryptText(string plainText, string key)
        {
            try
            {
                byte[] plainTextBytes = Encoding.UTF8.GetBytes(plainText);
                byte[] keyBytes = Encoding.UTF8.GetBytes(key);

                using (var aes = Aes.Create())
                {
                    aes.Key = ResizeKey(keyBytes, 32); // AES-256
                    aes.IV = new byte[16]; // IV صفري للبساطة

                    using (var encryptor = aes.CreateEncryptor())
                    {
                        byte[] encryptedBytes = encryptor.TransformFinalBlock(plainTextBytes, 0, plainTextBytes.Length);
                        return Convert.ToBase64String(encryptedBytes);
                    }
                }
            }
            catch
            {
                return plainText; // في حالة الفشل، إرجاع النص الأصلي
            }
        }

        /// <summary>
        /// فك تشفير نص
        /// </summary>
        /// <param name="encryptedText">النص المشفر</param>
        /// <param name="key">مفتاح التشفير</param>
        /// <returns>النص الأصلي</returns>
        public static string DecryptText(string encryptedText, string key)
        {
            try
            {
                byte[] encryptedBytes = Convert.FromBase64String(encryptedText);
                byte[] keyBytes = Encoding.UTF8.GetBytes(key);

                using (var aes = Aes.Create())
                {
                    aes.Key = ResizeKey(keyBytes, 32); // AES-256
                    aes.IV = new byte[16]; // IV صفري للبساطة

                    using (var decryptor = aes.CreateDecryptor())
                    {
                        byte[] decryptedBytes = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);
                        return Encoding.UTF8.GetString(decryptedBytes);
                    }
                }
            }
            catch
            {
                return encryptedText; // في حالة الفشل، إرجاع النص المشفر
            }
        }

        /// <summary>
        /// تغيير حجم المفتاح ليناسب خوارزمية التشفير
        /// </summary>
        /// <param name="key">المفتاح الأصلي</param>
        /// <param name="size">الحجم المطلوب</param>
        /// <returns>المفتاح بالحجم المطلوب</returns>
        private static byte[] ResizeKey(byte[] key, int size)
        {
            byte[] resizedKey = new byte[size];
            if (key.Length >= size)
            {
                Array.Copy(key, resizedKey, size);
            }
            else
            {
                Array.Copy(key, resizedKey, key.Length);
                // ملء الباقي بأصفار
            }
            return resizedKey;
        }
    }
}
