using CarSalesSystem.Core.Models;
using CarSalesSystem.Core.Enums;

namespace CarSalesSystem.Core.Interfaces
{
    /// <summary>
    /// واجهة مستودع السيارات
    /// </summary>
    public interface ICarRepository : IGenericRepository<Car>
    {
        /// <summary>
        /// الحصول على سيارة بواسطة رمز السيارة
        /// </summary>
        /// <param name="carCode">رمز السيارة</param>
        /// <returns>السيارة أو null</returns>
        Car? GetByCarCode(string carCode);

        /// <summary>
        /// الحصول على سيارة بواسطة رقم الشاسيه
        /// </summary>
        /// <param name="chassisNumber">رقم الشاسيه</param>
        /// <returns>السيارة أو null</returns>
        Car? GetByChassisNumber(string chassisNumber);

        /// <summary>
        /// البحث عن السيارات
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة السيارات</returns>
        IEnumerable<Car> SearchCars(string searchTerm);

        /// <summary>
        /// الحصول على السيارات المتاحة للبيع
        /// </summary>
        /// <returns>قائمة السيارات المتاحة</returns>
        IEnumerable<Car> GetAvailableCars();

        /// <summary>
        /// الحصول على السيارات المباعة
        /// </summary>
        /// <returns>قائمة السيارات المباعة</returns>
        IEnumerable<Car> GetSoldCars();

        /// <summary>
        /// الحصول على السيارات حسب الحالة
        /// </summary>
        /// <param name="condition">حالة السيارة</param>
        /// <returns>قائمة السيارات</returns>
        IEnumerable<Car> GetCarsByCondition(CarCondition condition);

        /// <summary>
        /// الحصول على السيارات حسب الماركة
        /// </summary>
        /// <param name="brand">الماركة</param>
        /// <returns>قائمة السيارات</returns>
        IEnumerable<Car> GetCarsByBrand(string brand);

        /// <summary>
        /// الحصول على السيارات حسب النموذج
        /// </summary>
        /// <param name="model">النموذج</param>
        /// <returns>قائمة السيارات</returns>
        IEnumerable<Car> GetCarsByModel(string model);

        /// <summary>
        /// الحصول على السيارات حسب السنة
        /// </summary>
        /// <param name="year">السنة</param>
        /// <returns>قائمة السيارات</returns>
        IEnumerable<Car> GetCarsByYear(int year);

        /// <summary>
        /// إنشاء رمز سيارة جديد
        /// </summary>
        /// <returns>رمز السيارة الجديد</returns>
        string GenerateCarCode();

        /// <summary>
        /// تحديث حالة السيارة إلى مباعة
        /// </summary>
        /// <param name="carId">معرف السيارة</param>
        void MarkAsSold(int carId);
    }
}
