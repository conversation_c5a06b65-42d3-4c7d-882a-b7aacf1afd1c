using CarSalesSystem.Core.Models;
using CarSalesSystem.Core.Enums;

namespace CarSalesSystem.Core.Interfaces
{
    /// <summary>
    /// واجهة مستودع المستخدمين
    /// </summary>
    public interface IUserRepository : IGenericRepository<User>
    {
        /// <summary>
        /// الحصول على مستخدم بواسطة اسم المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <returns>المستخدم أو null</returns>
        User? GetByUsername(string username);

        /// <summary>
        /// الحصول على المستخدمين حسب الدور
        /// </summary>
        /// <param name="role">الدور</param>
        /// <returns>قائمة المستخدمين</returns>
        IEnumerable<User> GetUsersByRole(UserRole role);

        /// <summary>
        /// التحقق من وجود اسم المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <returns>true إذا كان موجود</returns>
        bool IsUsernameExists(string username);

        /// <summary>
        /// الحصول على المستخدمين النشطين
        /// </summary>
        /// <returns>قائمة المستخدمين النشطين</returns>
        IEnumerable<User> GetActiveUsers();

        /// <summary>
        /// تحديث تاريخ آخر تسجيل دخول
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        void UpdateLastLoginDate(int userId);
    }
}
