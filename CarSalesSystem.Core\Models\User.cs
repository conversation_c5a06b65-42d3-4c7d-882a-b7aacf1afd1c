using CarSalesSystem.Core.Enums;
using System.ComponentModel.DataAnnotations;

namespace CarSalesSystem.Core.Models
{
    /// <summary>
    /// نموذج المستخدم
    /// </summary>
    public class User
    {
        [Key]
        public int UserId { get; set; }

        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string PasswordHash { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Email { get; set; }

        [StringLength(20)]
        public string? Phone { get; set; }

        [Required]
        public UserRole Role { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? LastLoginDate { get; set; }

        public int? CreatedBy { get; set; }

        // Navigation Properties
        public virtual User? Creator { get; set; }
        public virtual ICollection<User> CreatedUsers { get; set; } = new List<User>();
        public virtual ICollection<Customer> CreatedCustomers { get; set; } = new List<Customer>();
        public virtual ICollection<Customer> ModifiedCustomers { get; set; } = new List<Customer>();
        public virtual ICollection<Car> CreatedCars { get; set; } = new List<Car>();
        public virtual ICollection<Car> ModifiedCars { get; set; } = new List<Car>();
        public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();
        public virtual ICollection<Payment> ReceivedPayments { get; set; } = new List<Payment>();
        public virtual ICollection<Archive> UploadedArchives { get; set; } = new List<Archive>();
    }
}
