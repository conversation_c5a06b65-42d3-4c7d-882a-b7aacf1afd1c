using CarSalesSystem.Core.Models;

namespace CarSalesSystem.Core.Interfaces
{
    /// <summary>
    /// واجهة مستودع الأرشيف
    /// </summary>
    public interface IArchiveRepository : IGenericRepository<Archive>
    {
        /// <summary>
        /// الحصول على ملفات الأرشيف حسب نوع الكيان
        /// </summary>
        /// <param name="entityType">نوع الكيان</param>
        /// <param name="entityId">معرف الكيان</param>
        /// <returns>قائمة الملفات</returns>
        IEnumerable<Archive> GetArchivesByEntity(string entityType, int entityId);

        /// <summary>
        /// الحصول على ملفات الأرشيف حسب نوع الملف
        /// </summary>
        /// <param name="fileType">نوع الملف</param>
        /// <returns>قائمة الملفات</returns>
        IEnumerable<Archive> GetArchivesByFileType(string fileType);

        /// <summary>
        /// البحث في الأرشيف
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة الملفات</returns>
        IEnumerable<Archive> SearchArchives(string searchTerm);

        /// <summary>
        /// الحصول على الملفات النشطة
        /// </summary>
        /// <returns>قائمة الملفات النشطة</returns>
        IEnumerable<Archive> GetActiveArchives();

        /// <summary>
        /// الحصول على ملفات الأرشيف المرفوعة بواسطة مستخدم
        /// </summary>
        /// <param name="uploadedBy">معرف المستخدم</param>
        /// <returns>قائمة الملفات</returns>
        IEnumerable<Archive> GetArchivesByUploader(int uploadedBy);

        /// <summary>
        /// الحصول على ملفات الأرشيف في فترة زمنية
        /// </summary>
        /// <param name="startDate">تاريخ البداية</param>
        /// <param name="endDate">تاريخ النهاية</param>
        /// <returns>قائمة الملفات</returns>
        IEnumerable<Archive> GetArchivesByDateRange(DateTime startDate, DateTime endDate);

        /// <summary>
        /// حذف ملف من الأرشيف (تعطيل)
        /// </summary>
        /// <param name="archiveId">معرف الملف</param>
        void DeactivateArchive(int archiveId);

        /// <summary>
        /// استعادة ملف من الأرشيف
        /// </summary>
        /// <param name="archiveId">معرف الملف</param>
        void ReactivateArchive(int archiveId);

        /// <summary>
        /// الحصول على حجم الأرشيف الإجمالي
        /// </summary>
        /// <returns>الحجم بالبايت</returns>
        long GetTotalArchiveSize();

        /// <summary>
        /// الحصول على عدد الملفات في الأرشيف
        /// </summary>
        /// <returns>عدد الملفات</returns>
        int GetArchiveFilesCount();
    }
}
