using CarSalesSystem.Core.Models;
using Microsoft.EntityFrameworkCore;

namespace CarSalesSystem.Data.Context
{
    /// <summary>
    /// سياق قاعدة البيانات لنظام مبيعات السيارات
    /// </summary>
    public class CarSalesDbContext : DbContext
    {
        public CarSalesDbContext(DbContextOptions<CarSalesDbContext> options) : base(options)
        {
        }

        // DbSets
        public DbSet<User> Users { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Car> Cars { get; set; }
        public DbSet<Sale> Sales { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<Installment> Installments { get; set; }
        public DbSet<Archive> Archives { get; set; }
        public DbSet<ShowroomInfo> ShowroomInfos { get; set; }
        public DbSet<License> Licenses { get; set; }
        public DbSet<SystemSettings> SystemSettings { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين العلاقات والقيود
            ConfigureUserRelationships(modelBuilder);
            ConfigureCustomerRelationships(modelBuilder);
            ConfigureCarRelationships(modelBuilder);
            ConfigureSaleRelationships(modelBuilder);
            ConfigurePaymentRelationships(modelBuilder);
            ConfigureInstallmentRelationships(modelBuilder);
            ConfigureArchiveRelationships(modelBuilder);
            ConfigureShowroomRelationships(modelBuilder);
            ConfigureSystemSettingsRelationships(modelBuilder);

            // تكوين الفهارس
            ConfigureIndexes(modelBuilder);

            // تكوين القيم الافتراضية
            ConfigureDefaultValues(modelBuilder);
        }

        private void ConfigureUserRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.UserId);
                entity.HasIndex(e => e.Username).IsUnique();
                entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
                entity.Property(e => e.PasswordHash).IsRequired().HasMaxLength(255);
                entity.Property(e => e.FullName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Email).HasMaxLength(100);
                entity.Property(e => e.Phone).HasMaxLength(20);
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETDATE()");

                // العلاقة الذاتية للمستخدم المنشئ
                entity.HasOne(e => e.Creator)
                    .WithMany(e => e.CreatedUsers)
                    .HasForeignKey(e => e.CreatedBy)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }

        private void ConfigureCustomerRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.HasKey(e => e.CustomerId);
                entity.HasIndex(e => e.CustomerCode).IsUnique();
                entity.HasIndex(e => e.NationalId).IsUnique();
                entity.Property(e => e.CustomerCode).IsRequired().HasMaxLength(20);
                entity.Property(e => e.FullName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Phone).IsRequired().HasMaxLength(20);
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.CanBeDeleted).HasDefaultValue(true);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETDATE()");

                // العلاقة مع المستخدم المنشئ
                entity.HasOne(e => e.Creator)
                    .WithMany(e => e.CreatedCustomers)
                    .HasForeignKey(e => e.CreatedBy)
                    .OnDelete(DeleteBehavior.Restrict);

                // العلاقة مع المستخدم المعدل
                entity.HasOne(e => e.Modifier)
                    .WithMany(e => e.ModifiedCustomers)
                    .HasForeignKey(e => e.ModifiedBy)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }

        private void ConfigureCarRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Car>(entity =>
            {
                entity.HasKey(e => e.CarId);
                entity.HasIndex(e => e.CarCode).IsUnique();
                entity.HasIndex(e => e.ChassisNumber).IsUnique();
                entity.Property(e => e.CarCode).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Brand).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Model).IsRequired().HasMaxLength(50);
                entity.Property(e => e.SalePrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.PurchasePrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.IsAvailable).HasDefaultValue(true);
                entity.Property(e => e.IsSold).HasDefaultValue(false);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETDATE()");

                // العلاقة مع المستخدم المنشئ
                entity.HasOne(e => e.Creator)
                    .WithMany(e => e.CreatedCars)
                    .HasForeignKey(e => e.CreatedBy)
                    .OnDelete(DeleteBehavior.Restrict);

                // العلاقة مع المستخدم المعدل
                entity.HasOne(e => e.Modifier)
                    .WithMany(e => e.ModifiedCars)
                    .HasForeignKey(e => e.ModifiedBy)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }

        private void ConfigureSaleRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Sale>(entity =>
            {
                entity.HasKey(e => e.SaleId);
                entity.HasIndex(e => e.SaleNumber).IsUnique();
                entity.Property(e => e.SaleNumber).IsRequired().HasMaxLength(20);
                entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.DownPayment).HasColumnType("decimal(18,2)");
                entity.Property(e => e.RemainingAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.MonthlyInstallment).HasColumnType("decimal(18,2)");
                entity.Property(e => e.InterestRate).HasColumnType("decimal(5,2)");
                entity.Property(e => e.IsCompleted).HasDefaultValue(false);
                entity.Property(e => e.SaleDate).HasDefaultValueSql("GETDATE()");
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETDATE()");

                // العلاقة مع العميل
                entity.HasOne(e => e.Customer)
                    .WithMany(e => e.Sales)
                    .HasForeignKey(e => e.CustomerId)
                    .OnDelete(DeleteBehavior.Restrict);

                // العلاقة مع السيارة
                entity.HasOne(e => e.Car)
                    .WithMany(e => e.Sales)
                    .HasForeignKey(e => e.CarId)
                    .OnDelete(DeleteBehavior.Restrict);

                // العلاقة مع مندوب المبيعات
                entity.HasOne(e => e.SalesAgent)
                    .WithMany(e => e.Sales)
                    .HasForeignKey(e => e.SalesAgentId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }

        private void ConfigurePaymentRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Payment>(entity =>
            {
                entity.HasKey(e => e.PaymentId);
                entity.HasIndex(e => e.PaymentNumber).IsUnique();
                entity.Property(e => e.PaymentNumber).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.PaymentDate).HasDefaultValueSql("GETDATE()");
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETDATE()");

                // العلاقة مع المبيعة
                entity.HasOne(e => e.Sale)
                    .WithMany(e => e.Payments)
                    .HasForeignKey(e => e.SaleId)
                    .OnDelete(DeleteBehavior.Restrict);

                // العلاقة مع المستخدم المستلم
                entity.HasOne(e => e.Receiver)
                    .WithMany(e => e.ReceivedPayments)
                    .HasForeignKey(e => e.ReceivedBy)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }

        private void ConfigureInstallmentRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Installment>(entity =>
            {
                entity.HasKey(e => e.InstallmentId);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.PaidAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.RemainingAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.LateFee).HasColumnType("decimal(18,2)");
                entity.Property(e => e.IsPaid).HasDefaultValue(false);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETDATE()");

                // العلاقة مع المبيعة
                entity.HasOne(e => e.Sale)
                    .WithMany(e => e.Installments)
                    .HasForeignKey(e => e.SaleId)
                    .OnDelete(DeleteBehavior.Restrict);

                // العلاقة مع الدفعة
                entity.HasOne(e => e.Payment)
                    .WithMany(e => e.Installments)
                    .HasForeignKey(e => e.PaymentId)
                    .OnDelete(DeleteBehavior.SetNull);
            });
        }

        private void ConfigureArchiveRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Archive>(entity =>
            {
                entity.HasKey(e => e.ArchiveId);
                entity.Property(e => e.EntityType).IsRequired().HasMaxLength(50);
                entity.Property(e => e.FileName).IsRequired().HasMaxLength(255);
                entity.Property(e => e.OriginalFileName).IsRequired().HasMaxLength(255);
                entity.Property(e => e.FilePath).IsRequired().HasMaxLength(500);
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.UploadedDate).HasDefaultValueSql("GETDATE()");

                // العلاقة مع المستخدم الرافع
                entity.HasOne(e => e.Uploader)
                    .WithMany(e => e.UploadedArchives)
                    .HasForeignKey(e => e.UploadedBy)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }

        private void ConfigureShowroomRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ShowroomInfo>(entity =>
            {
                entity.HasKey(e => e.ShowroomId);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.ModifiedDate).HasDefaultValueSql("GETDATE()");

                // العلاقة مع المستخدم المعدل
                entity.HasOne(e => e.Modifier)
                    .WithMany()
                    .HasForeignKey(e => e.ModifiedBy)
                    .OnDelete(DeleteBehavior.SetNull);
            });
        }

        private void ConfigureSystemSettingsRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<SystemSettings>(entity =>
            {
                entity.HasKey(e => e.SettingId);
                entity.HasIndex(e => e.SettingKey).IsUnique();
                entity.Property(e => e.SettingKey).IsRequired().HasMaxLength(100);
                entity.Property(e => e.ModifiedDate).HasDefaultValueSql("GETDATE()");

                // العلاقة مع المستخدم المعدل
                entity.HasOne(e => e.Modifier)
                    .WithMany()
                    .HasForeignKey(e => e.ModifiedBy)
                    .OnDelete(DeleteBehavior.SetNull);
            });
        }

        private void ConfigureIndexes(ModelBuilder modelBuilder)
        {
            // فهارس إضافية لتحسين الأداء
            modelBuilder.Entity<Customer>()
                .HasIndex(e => e.Phone);

            modelBuilder.Entity<Car>()
                .HasIndex(e => new { e.Brand, e.Model });

            modelBuilder.Entity<Sale>()
                .HasIndex(e => e.SaleDate);

            modelBuilder.Entity<Payment>()
                .HasIndex(e => e.PaymentDate);

            modelBuilder.Entity<Installment>()
                .HasIndex(e => e.DueDate);

            modelBuilder.Entity<License>()
                .HasIndex(e => e.LicenseKey).IsUnique();

            modelBuilder.Entity<License>()
                .HasIndex(e => e.MachineId);
        }

        private void ConfigureDefaultValues(ModelBuilder modelBuilder)
        {
            // تكوين القيم الافتراضية للترخيص
            modelBuilder.Entity<License>(entity =>
            {
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETDATE()");
            });
        }
    }
}
