using CarSalesSystem.Core.Models;

namespace CarSalesSystem.Core.Interfaces
{
    /// <summary>
    /// واجهة مستودع العملاء
    /// </summary>
    public interface ICustomerRepository : IGenericRepository<Customer>
    {
        /// <summary>
        /// الحصول على عميل بواسطة رمز العميل
        /// </summary>
        /// <param name="customerCode">رمز العميل</param>
        /// <returns>العميل أو null</returns>
        Customer? GetByCustomerCode(string customerCode);

        /// <summary>
        /// الحصول على عميل بواسطة رقم الهوية
        /// </summary>
        /// <param name="nationalId">رقم الهوية</param>
        /// <returns>العميل أو null</returns>
        Customer? GetByNationalId(string nationalId);

        /// <summary>
        /// البحث عن العملاء بواسطة الاسم أو الهاتف
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة العملاء</returns>
        IEnumerable<Customer> SearchCustomers(string searchTerm);

        /// <summary>
        /// الحصول على العملاء النشطين
        /// </summary>
        /// <returns>قائمة العملاء النشطين</returns>
        IEnumerable<Customer> GetActiveCustomers();

        /// <summary>
        /// الحصول على العملاء الذين لديهم أقساط مستحقة
        /// </summary>
        /// <returns>قائمة العملاء</returns>
        IEnumerable<Customer> GetCustomersWithDueInstallments();

        /// <summary>
        /// التحقق من إمكانية حذف العميل
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <returns>true إذا كان يمكن حذفه</returns>
        bool CanDeleteCustomer(int customerId);

        /// <summary>
        /// إنشاء رمز عميل جديد
        /// </summary>
        /// <returns>رمز العميل الجديد</returns>
        string GenerateCustomerCode();
    }
}
