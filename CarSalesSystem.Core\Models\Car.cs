using CarSalesSystem.Core.Enums;
using System.ComponentModel.DataAnnotations;

namespace CarSalesSystem.Core.Models
{
    /// <summary>
    /// نموذج السيارة
    /// </summary>
    public class Car
    {
        [Key]
        public int CarId { get; set; }

        [Required]
        [StringLength(20)]
        public string CarCode { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Brand { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Model { get; set; } = string.Empty;

        [Required]
        public int Year { get; set; }

        [StringLength(30)]
        public string? Color { get; set; }

        [StringLength(50)]
        public string? ChassisNumber { get; set; }

        [StringLength(50)]
        public string? EngineNumber { get; set; }

        [Required]
        public CarCondition Condition { get; set; }

        public decimal? PurchasePrice { get; set; }

        [Required]
        public decimal SalePrice { get; set; }

        public int? Mileage { get; set; }

        [StringLength(20)]
        public string? FuelType { get; set; }

        [StringLength(20)]
        public string? Transmission { get; set; }

        [StringLength(20)]
        public string? EngineCapacity { get; set; }

        public bool IsAvailable { get; set; } = true;

        public bool IsSold { get; set; } = false;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Required]
        public int CreatedBy { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public int? ModifiedBy { get; set; }

        public DateTime? SoldDate { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual User Creator { get; set; } = null!;
        public virtual User? Modifier { get; set; }
        public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();
    }
}
