using CarSalesSystem.Core.Interfaces;
using CarSalesSystem.Core.Models;
using CarSalesSystem.Core.Enums;
using CarSalesSystem.Business.Helpers;

namespace CarSalesSystem.Business.Services
{
    /// <summary>
    /// خدمة إدارة المستخدمين
    /// </summary>
    public class UserService
    {
        private readonly IUserRepository _userRepository;
        private const string DEVELOPER_SECRET = "abrar";

        public UserService(IUserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        /// <summary>
        /// تسجيل دخول المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>المستخدم أو null</returns>
        public User? Authenticate(string username, string password)
        {
            var user = _userRepository.GetByUsername(username);
            if (user == null || !user.IsActive)
            {
                return null;
            }

            if (!EncryptionHelper.VerifyPasswordHash(password, user.PasswordHash))
            {
                return null;
            }

            // تحديث تاريخ آخر تسجيل دخول
            _userRepository.UpdateLastLoginDate(user.UserId);
            
            return user;
        }

        /// <summary>
        /// إنشاء مستخدم جديد
        /// </summary>
        /// <param name="user">بيانات المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <param name="createdBy">معرف المستخدم المنشئ</param>
        /// <returns>المستخدم المنشأ</returns>
        public User CreateUser(User user, string password, int createdBy)
        {
            // التحقق من عدم وجود اسم المستخدم
            if (_userRepository.IsUsernameExists(user.Username))
            {
                throw new InvalidOperationException("اسم المستخدم موجود بالفعل.");
            }

            // تشفير كلمة المرور
            user.PasswordHash = EncryptionHelper.HashPassword(password);
            user.CreatedBy = createdBy;
            user.CreatedDate = DateTime.Now;

            _userRepository.Add(user);
            _userRepository.SaveChanges();

            return user;
        }

        /// <summary>
        /// تحديث بيانات المستخدم
        /// </summary>
        /// <param name="user">بيانات المستخدم</param>
        public void UpdateUser(User user)
        {
            var existingUser = _userRepository.GetById(user.UserId);
            if (existingUser == null)
            {
                throw new InvalidOperationException("المستخدم غير موجود.");
            }

            // التحقق من عدم تغيير اسم المستخدم إلى اسم موجود
            if (existingUser.Username != user.Username && _userRepository.IsUsernameExists(user.Username))
            {
                throw new InvalidOperationException("اسم المستخدم موجود بالفعل.");
            }

            existingUser.Username = user.Username;
            existingUser.FullName = user.FullName;
            existingUser.Email = user.Email;
            existingUser.Phone = user.Phone;
            existingUser.Role = user.Role;
            existingUser.IsActive = user.IsActive;

            _userRepository.Update(existingUser);
            _userRepository.SaveChanges();
        }

        /// <summary>
        /// تغيير كلمة مرور المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="oldPassword">كلمة المرور القديمة</param>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        public void ChangePassword(int userId, string oldPassword, string newPassword)
        {
            var user = _userRepository.GetById(userId);
            if (user == null)
            {
                throw new InvalidOperationException("المستخدم غير موجود.");
            }

            if (!EncryptionHelper.VerifyPasswordHash(oldPassword, user.PasswordHash))
            {
                throw new InvalidOperationException("كلمة المرور القديمة غير صحيحة.");
            }

            user.PasswordHash = EncryptionHelper.HashPassword(newPassword);
            _userRepository.Update(user);
            _userRepository.SaveChanges();
        }

        /// <summary>
        /// إعادة تعيين كلمة مرور المستخدم (للمدير فقط)
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        /// <param name="resetBy">معرف المستخدم الذي يقوم بالإعادة</param>
        public void ResetPassword(int userId, string newPassword, int resetBy)
        {
            var user = _userRepository.GetById(userId);
            if (user == null)
            {
                throw new InvalidOperationException("المستخدم غير موجود.");
            }

            var resetByUser = _userRepository.GetById(resetBy);
            if (resetByUser == null || (resetByUser.Role != UserRole.Manager && resetByUser.Role != UserRole.Developer))
            {
                throw new UnauthorizedAccessException("ليس لديك صلاحية لإعادة تعيين كلمة المرور.");
            }

            user.PasswordHash = EncryptionHelper.HashPassword(newPassword);
            _userRepository.Update(user);
            _userRepository.SaveChanges();
        }

        /// <summary>
        /// حذف مستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="deletedBy">معرف المستخدم الذي يقوم بالحذف</param>
        public void DeleteUser(int userId, int deletedBy)
        {
            var user = _userRepository.GetById(userId);
            if (user == null)
            {
                throw new InvalidOperationException("المستخدم غير موجود.");
            }

            var deletedByUser = _userRepository.GetById(deletedBy);
            if (deletedByUser == null || (deletedByUser.Role != UserRole.Manager && deletedByUser.Role != UserRole.Developer))
            {
                throw new UnauthorizedAccessException("ليس لديك صلاحية لحذف المستخدمين.");
            }

            // لا يمكن حذف المطورين
            if (user.Role == UserRole.Developer)
            {
                throw new InvalidOperationException("لا يمكن حذف المطورين.");
            }

            // تعطيل المستخدم بدلاً من حذفه
            user.IsActive = false;
            _userRepository.Update(user);
            _userRepository.SaveChanges();
        }

        /// <summary>
        /// الحصول على جميع المستخدمين
        /// </summary>
        /// <returns>قائمة المستخدمين</returns>
        public IEnumerable<User> GetAllUsers()
        {
            return _userRepository.GetAll();
        }

        /// <summary>
        /// الحصول على المستخدمين النشطين
        /// </summary>
        /// <returns>قائمة المستخدمين النشطين</returns>
        public IEnumerable<User> GetActiveUsers()
        {
            return _userRepository.GetActiveUsers();
        }

        /// <summary>
        /// الحصول على مستخدم بالمعرف
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>المستخدم أو null</returns>
        public User? GetUserById(int userId)
        {
            return _userRepository.GetById(userId);
        }

        /// <summary>
        /// الحصول على مندوبي المبيعات
        /// </summary>
        /// <returns>قائمة مندوبي المبيعات</returns>
        public IEnumerable<User> GetSalesAgents()
        {
            return _userRepository.GetUsersByRole(UserRole.SalesAgent);
        }

        /// <summary>
        /// الحصول على المديرين
        /// </summary>
        /// <returns>قائمة المديرين</returns>
        public IEnumerable<User> GetManagers()
        {
            return _userRepository.GetUsersByRole(UserRole.Manager);
        }

        /// <summary>
        /// التحقق من صلاحيات المطور
        /// </summary>
        /// <param name="secretKey">المفتاح السري</param>
        /// <returns>true إذا كان المفتاح صحيح</returns>
        public bool IsDeveloperModeEnabled(string secretKey)
        {
            return secretKey == DEVELOPER_SECRET;
        }

        /// <summary>
        /// التحقق من صلاحية المستخدم لعملية معينة
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="requiredRole">الدور المطلوب</param>
        /// <returns>true إذا كان لديه الصلاحية</returns>
        public bool HasPermission(int userId, UserRole requiredRole)
        {
            var user = _userRepository.GetById(userId);
            if (user == null || !user.IsActive)
            {
                return false;
            }

            // المطور لديه جميع الصلاحيات
            if (user.Role == UserRole.Developer)
            {
                return true;
            }

            // المدير لديه صلاحيات المدير ومندوب المبيعات
            if (user.Role == UserRole.Manager && (requiredRole == UserRole.Manager || requiredRole == UserRole.SalesAgent))
            {
                return true;
            }

            // مندوب المبيعات لديه صلاحيات مندوب المبيعات فقط
            return user.Role == requiredRole;
        }

        /// <summary>
        /// إنشاء مستخدم مطور افتراضي
        /// </summary>
        public void CreateDefaultDeveloper()
        {
            if (!_userRepository.GetUsersByRole(UserRole.Developer).Any())
            {
                var developer = new User
                {
                    Username = "developer",
                    FullName = "مطور النظام",
                    Role = UserRole.Developer,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };

                developer.PasswordHash = EncryptionHelper.HashPassword("123456");
                
                _userRepository.Add(developer);
                _userRepository.SaveChanges();
            }
        }
    }
}
