<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CarSalesSystem.Core\CarSalesSystem.Core.csproj" />
    <ProjectReference Include="..\CarSalesSystem.Data\CarSalesSystem.Data.csproj" />
    <ProjectReference Include="..\CarSalesSystem.Business\CarSalesSystem.Business.csproj" />
    <ProjectReference Include="..\CarSalesSystem.Reports\CarSalesSystem.Reports.csproj" />
  </ItemGroup>

</Project>
