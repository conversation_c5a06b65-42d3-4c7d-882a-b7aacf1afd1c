using CarSalesSystem.Core.Interfaces;
using CarSalesSystem.Core.Models;
using CarSalesSystem.Data.Context;
using Microsoft.EntityFrameworkCore;

namespace CarSalesSystem.Data.Repositories
{
    /// <summary>
    /// تنفيذ مستودع الدفعات
    /// </summary>
    public class PaymentRepository : GenericRepository<Payment>, IPaymentRepository
    {
        public PaymentRepository(CarSalesDbContext context) : base(context)
        {
        }

        public Payment? GetByPaymentNumber(string paymentNumber)
        {
            return _dbSet.Include(p => p.Sale)
                         .ThenInclude(s => s.Customer)
                         .Include(p => p.Sale)
                         .ThenInclude(s => s.Car)
                         .Include(p => p.Receiver)
                         .Include(p => p.Installments)
                         .FirstOrDefault(p => p.PaymentNumber == paymentNumber);
        }

        public IEnumerable<Payment> GetPaymentsBySaleId(int saleId)
        {
            return _dbSet.Include(p => p.Sale)
                         .ThenInclude(s => s.Customer)
                         .Include(p => p.Sale)
                         .ThenInclude(s => s.Car)
                         .Include(p => p.Receiver)
                         .Include(p => p.Installments)
                         .Where(p => p.SaleId == saleId)
                         .ToList();
        }

        public IEnumerable<Payment> GetPaymentsByCustomerId(int customerId)
        {
            return _dbSet.Include(p => p.Sale)
                         .ThenInclude(s => s.Customer)
                         .Include(p => p.Sale)
                         .ThenInclude(s => s.Car)
                         .Include(p => p.Receiver)
                         .Include(p => p.Installments)
                         .Where(p => p.Sale.CustomerId == customerId)
                         .ToList();
        }

        public IEnumerable<Payment> GetPaymentsByDateRange(DateTime startDate, DateTime endDate)
        {
            return _dbSet.Include(p => p.Sale)
                         .ThenInclude(s => s.Customer)
                         .Include(p => p.Sale)
                         .ThenInclude(s => s.Car)
                         .Include(p => p.Receiver)
                         .Include(p => p.Installments)
                         .Where(p => p.PaymentDate >= startDate && p.PaymentDate <= endDate)
                         .ToList();
        }

        public IEnumerable<Payment> GetPaymentsByReceiver(int receivedBy)
        {
            return _dbSet.Include(p => p.Sale)
                         .ThenInclude(s => s.Customer)
                         .Include(p => p.Sale)
                         .ThenInclude(s => s.Car)
                         .Include(p => p.Receiver)
                         .Include(p => p.Installments)
                         .Where(p => p.ReceivedBy == receivedBy)
                         .ToList();
        }

        public string GeneratePaymentNumber()
        {
            var today = DateTime.Now;
            var prefix = $"P{today:yyyyMMdd}";
            
            var lastPayment = _dbSet.Where(p => p.PaymentNumber.StartsWith(prefix))
                                    .OrderByDescending(p => p.PaymentNumber)
                                    .FirstOrDefault();

            int nextSequence = 1;
            if (lastPayment != null)
            {
                var lastSequence = lastPayment.PaymentNumber.Substring(prefix.Length);
                if (int.TryParse(lastSequence, out int seq))
                {
                    nextSequence = seq + 1;
                }
            }

            return $"{prefix}{nextSequence:D3}"; // مثال: P20241201001
        }

        public decimal GetTotalPaymentsForSale(int saleId)
        {
            return _dbSet.Where(p => p.SaleId == saleId)
                         .Sum(p => p.Amount);
        }

        public decimal GetTotalPaymentsAmount(DateTime startDate, DateTime endDate)
        {
            return _dbSet.Where(p => p.PaymentDate >= startDate && p.PaymentDate <= endDate)
                         .Sum(p => p.Amount);
        }

        public override IEnumerable<Payment> GetAll()
        {
            return _dbSet.Include(p => p.Sale)
                         .ThenInclude(s => s.Customer)
                         .Include(p => p.Sale)
                         .ThenInclude(s => s.Car)
                         .Include(p => p.Receiver)
                         .Include(p => p.Installments)
                         .ToList();
        }

        public override Payment? GetById(int id)
        {
            return _dbSet.Include(p => p.Sale)
                         .ThenInclude(s => s.Customer)
                         .Include(p => p.Sale)
                         .ThenInclude(s => s.Car)
                         .Include(p => p.Receiver)
                         .Include(p => p.Installments)
                         .FirstOrDefault(p => p.PaymentId == id);
        }
    }
}
