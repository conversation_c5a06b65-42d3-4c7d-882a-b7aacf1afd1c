using CarSalesSystem.Core.Interfaces;
using CarSalesSystem.Core.Models;

namespace CarSalesSystem.Business.Services
{
    /// <summary>
    /// خدمة إدارة العملاء
    /// </summary>
    public class CustomerService
    {
        private readonly ICustomerRepository _customerRepository;
        private readonly ISaleRepository _saleRepository;

        public CustomerService(ICustomerRepository customerRepository, ISaleRepository saleRepository)
        {
            _customerRepository = customerRepository;
            _saleRepository = saleRepository;
        }

        /// <summary>
        /// إنشاء عميل جديد
        /// </summary>
        /// <param name="customer">بيانات العميل</param>
        /// <param name="createdBy">معرف المستخدم المنشئ</param>
        /// <returns>العميل المنشأ</returns>
        public Customer CreateCustomer(Customer customer, int createdBy)
        {
            // التحقق من عدم وجود رقم الهوية
            if (!string.IsNullOrEmpty(customer.NationalId))
            {
                var existingCustomer = _customerRepository.GetByNationalId(customer.NationalId);
                if (existingCustomer != null)
                {
                    throw new InvalidOperationException("رقم الهوية موجود بالفعل.");
                }
            }

            // إنشاء رمز العميل
            customer.CustomerCode = _customerRepository.GenerateCustomerCode();
            customer.CreatedBy = createdBy;
            customer.CreatedDate = DateTime.Now;

            _customerRepository.Add(customer);
            _customerRepository.SaveChanges();

            return customer;
        }

        /// <summary>
        /// تحديث بيانات العميل
        /// </summary>
        /// <param name="customer">بيانات العميل</param>
        /// <param name="modifiedBy">معرف المستخدم المعدل</param>
        public void UpdateCustomer(Customer customer, int modifiedBy)
        {
            var existingCustomer = _customerRepository.GetById(customer.CustomerId);
            if (existingCustomer == null)
            {
                throw new InvalidOperationException("العميل غير موجود.");
            }

            // التحقق من عدم تغيير رقم الهوية إلى رقم موجود
            if (!string.IsNullOrEmpty(customer.NationalId) && 
                existingCustomer.NationalId != customer.NationalId)
            {
                var duplicateCustomer = _customerRepository.GetByNationalId(customer.NationalId);
                if (duplicateCustomer != null)
                {
                    throw new InvalidOperationException("رقم الهوية موجود بالفعل.");
                }
            }

            existingCustomer.FullName = customer.FullName;
            existingCustomer.NationalId = customer.NationalId;
            existingCustomer.Phone = customer.Phone;
            existingCustomer.AlternatePhone = customer.AlternatePhone;
            existingCustomer.Email = customer.Email;
            existingCustomer.Address = customer.Address;
            existingCustomer.City = customer.City;
            existingCustomer.DateOfBirth = customer.DateOfBirth;
            existingCustomer.Occupation = customer.Occupation;
            existingCustomer.MonthlyIncome = customer.MonthlyIncome;
            existingCustomer.Notes = customer.Notes;
            existingCustomer.ModifiedBy = modifiedBy;
            existingCustomer.ModifiedDate = DateTime.Now;

            _customerRepository.Update(existingCustomer);
            _customerRepository.SaveChanges();
        }

        /// <summary>
        /// حذف عميل
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        public void DeleteCustomer(int customerId)
        {
            var customer = _customerRepository.GetById(customerId);
            if (customer == null)
            {
                throw new InvalidOperationException("العميل غير موجود.");
            }

            if (!_customerRepository.CanDeleteCustomer(customerId))
            {
                throw new InvalidOperationException("لا يمكن حذف العميل لوجود مبيعات بالتقسيط غير مكتملة.");
            }

            _customerRepository.Remove(customer);
            _customerRepository.SaveChanges();
        }

        /// <summary>
        /// تعطيل عميل
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        public void DeactivateCustomer(int customerId)
        {
            var customer = _customerRepository.GetById(customerId);
            if (customer == null)
            {
                throw new InvalidOperationException("العميل غير موجود.");
            }

            customer.IsActive = false;
            _customerRepository.Update(customer);
            _customerRepository.SaveChanges();
        }

        /// <summary>
        /// تفعيل عميل
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        public void ActivateCustomer(int customerId)
        {
            var customer = _customerRepository.GetById(customerId);
            if (customer == null)
            {
                throw new InvalidOperationException("العميل غير موجود.");
            }

            customer.IsActive = true;
            _customerRepository.Update(customer);
            _customerRepository.SaveChanges();
        }

        /// <summary>
        /// استبدال عميل في التقسيط
        /// </summary>
        /// <param name="oldCustomerId">معرف العميل القديم</param>
        /// <param name="newCustomerId">معرف العميل الجديد</param>
        /// <param name="saleId">معرف المبيعة</param>
        public void ReplaceCustomerInInstallment(int oldCustomerId, int newCustomerId, int saleId)
        {
            var sale = _saleRepository.GetById(saleId);
            if (sale == null || sale.PaymentMethod != Core.Enums.PaymentMethod.Installment)
            {
                throw new InvalidOperationException("عملية البيع غير موجودة أو ليست بالتقسيط.");
            }

            var oldCustomer = _customerRepository.GetById(oldCustomerId);
            var newCustomer = _customerRepository.GetById(newCustomerId);

            if (oldCustomer == null || newCustomer == null)
            {
                throw new InvalidOperationException("العميل القديم أو الجديد غير موجود.");
            }

            if (sale.CustomerId != oldCustomerId)
            {
                throw new InvalidOperationException("العميل القديم المحدد لا يتطابق مع العميل المرتبط بعملية البيع.");
            }

            // تحديث العميل في عملية البيع
            sale.CustomerId = newCustomerId;
            _saleRepository.Update(sale);
            _saleRepository.SaveChanges();

            // تحديث حالة العملاء
            oldCustomer.CanBeDeleted = true;
            newCustomer.CanBeDeleted = false;

            _customerRepository.Update(oldCustomer);
            _customerRepository.Update(newCustomer);
            _customerRepository.SaveChanges();
        }

        /// <summary>
        /// الحصول على جميع العملاء
        /// </summary>
        /// <returns>قائمة العملاء</returns>
        public IEnumerable<Customer> GetAllCustomers()
        {
            return _customerRepository.GetAll();
        }

        /// <summary>
        /// الحصول على العملاء النشطين
        /// </summary>
        /// <returns>قائمة العملاء النشطين</returns>
        public IEnumerable<Customer> GetActiveCustomers()
        {
            return _customerRepository.GetActiveCustomers();
        }

        /// <summary>
        /// الحصول على عميل بالمعرف
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <returns>العميل أو null</returns>
        public Customer? GetCustomerById(int customerId)
        {
            return _customerRepository.GetById(customerId);
        }

        /// <summary>
        /// الحصول على عميل برمز العميل
        /// </summary>
        /// <param name="customerCode">رمز العميل</param>
        /// <returns>العميل أو null</returns>
        public Customer? GetCustomerByCode(string customerCode)
        {
            return _customerRepository.GetByCustomerCode(customerCode);
        }

        /// <summary>
        /// الحصول على عميل برقم الهوية
        /// </summary>
        /// <param name="nationalId">رقم الهوية</param>
        /// <returns>العميل أو null</returns>
        public Customer? GetCustomerByNationalId(string nationalId)
        {
            return _customerRepository.GetByNationalId(nationalId);
        }

        /// <summary>
        /// البحث عن العملاء
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة العملاء</returns>
        public IEnumerable<Customer> SearchCustomers(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return GetActiveCustomers();
            }

            return _customerRepository.SearchCustomers(searchTerm);
        }

        /// <summary>
        /// الحصول على العملاء الذين لديهم أقساط مستحقة
        /// </summary>
        /// <returns>قائمة العملاء</returns>
        public IEnumerable<Customer> GetCustomersWithDueInstallments()
        {
            return _customerRepository.GetCustomersWithDueInstallments();
        }

        /// <summary>
        /// التحقق من إمكانية حذف العميل
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <returns>true إذا كان يمكن حذفه</returns>
        public bool CanDeleteCustomer(int customerId)
        {
            return _customerRepository.CanDeleteCustomer(customerId);
        }

        /// <summary>
        /// الحصول على إحصائيات العملاء
        /// </summary>
        /// <returns>إحصائيات العملاء</returns>
        public CustomerStatistics GetCustomerStatistics()
        {
            var allCustomers = _customerRepository.GetAll().ToList();
            var activeCustomers = allCustomers.Where(c => c.IsActive).ToList();
            var customersWithDueInstallments = _customerRepository.GetCustomersWithDueInstallments().ToList();

            return new CustomerStatistics
            {
                TotalCustomers = allCustomers.Count,
                ActiveCustomers = activeCustomers.Count,
                InactiveCustomers = allCustomers.Count - activeCustomers.Count,
                CustomersWithDueInstallments = customersWithDueInstallments.Count
            };
        }
    }

    /// <summary>
    /// إحصائيات العملاء
    /// </summary>
    public class CustomerStatistics
    {
        public int TotalCustomers { get; set; }
        public int ActiveCustomers { get; set; }
        public int InactiveCustomers { get; set; }
        public int CustomersWithDueInstallments { get; set; }
    }
}
