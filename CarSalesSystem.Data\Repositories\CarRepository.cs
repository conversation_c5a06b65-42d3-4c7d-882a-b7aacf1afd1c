using CarSalesSystem.Core.Interfaces;
using CarSalesSystem.Core.Models;
using CarSalesSystem.Core.Enums;
using CarSalesSystem.Data.Context;
using Microsoft.EntityFrameworkCore;

namespace CarSalesSystem.Data.Repositories
{
    /// <summary>
    /// تنفيذ مستودع السيارات
    /// </summary>
    public class CarRepository : GenericRepository<Car>, ICarRepository
    {
        public CarRepository(CarSalesDbContext context) : base(context)
        {
        }

        public Car? GetByCarCode(string carCode)
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .FirstOrDefault(c => c.CarCode == carCode);
        }

        public Car? GetByChassisNumber(string chassisNumber)
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .FirstOrDefault(c => c.ChassisNumber == chassisNumber);
        }

        public IEnumerable<Car> SearchCars(string searchTerm)
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .Where(c => c.Brand.Contains(searchTerm) ||
                                   c.Model.Contains(searchTerm) ||
                                   c.CarCode.Contains(searchTerm) ||
                                   (c.ChassisNumber != null && c.ChassisNumber.Contains(searchTerm)) ||
                                   (c.Color != null && c.Color.Contains(searchTerm)))
                         .ToList();
        }

        public IEnumerable<Car> GetAvailableCars()
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .Where(c => c.IsAvailable && !c.IsSold)
                         .ToList();
        }

        public IEnumerable<Car> GetSoldCars()
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .Where(c => c.IsSold)
                         .ToList();
        }

        public IEnumerable<Car> GetCarsByCondition(CarCondition condition)
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .Where(c => c.Condition == condition)
                         .ToList();
        }

        public IEnumerable<Car> GetCarsByBrand(string brand)
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .Where(c => c.Brand == brand)
                         .ToList();
        }

        public IEnumerable<Car> GetCarsByModel(string model)
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .Where(c => c.Model == model)
                         .ToList();
        }

        public IEnumerable<Car> GetCarsByYear(int year)
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .Where(c => c.Year == year)
                         .ToList();
        }

        public string GenerateCarCode()
        {
            var lastCar = _dbSet.OrderByDescending(c => c.CarId).FirstOrDefault();
            var nextId = (lastCar?.CarId ?? 0) + 1;
            return $"CAR{nextId:D6}"; // مثال: CAR000001
        }

        public void MarkAsSold(int carId)
        {
            var car = _dbSet.Find(carId);
            if (car != null)
            {
                car.IsSold = true;
                car.IsAvailable = false;
                car.SoldDate = DateTime.Now;
                _context.SaveChanges();
            }
        }

        public override IEnumerable<Car> GetAll()
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .ToList();
        }

        public override Car? GetById(int id)
        {
            return _dbSet.Include(c => c.Creator)
                         .Include(c => c.Modifier)
                         .Include(c => c.Sales)
                         .FirstOrDefault(c => c.CarId == id);
        }
    }
}
