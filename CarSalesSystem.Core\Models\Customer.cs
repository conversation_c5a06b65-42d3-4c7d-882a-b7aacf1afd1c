using System.ComponentModel.DataAnnotations;

namespace CarSalesSystem.Core.Models
{
    /// <summary>
    /// نموذج العميل
    /// </summary>
    public class Customer
    {
        [Key]
        public int CustomerId { get; set; }

        [Required]
        [StringLength(20)]
        public string CustomerCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;

        [StringLength(20)]
        public string? NationalId { get; set; }

        [Required]
        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;

        [StringLength(20)]
        public string? AlternatePhone { get; set; }

        [StringLength(100)]
        public string? Email { get; set; }

        [StringLength(500)]
        public string? Address { get; set; }

        [StringLength(50)]
        public string? City { get; set; }

        public DateTime? DateOfBirth { get; set; }

        [StringLength(100)]
        public string? Occupation { get; set; }

        public decimal? MonthlyIncome { get; set; }

        public bool IsActive { get; set; } = true;

        public bool CanBeDeleted { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Required]
        public int CreatedBy { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public int? ModifiedBy { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual User Creator { get; set; } = null!;
        public virtual User? Modifier { get; set; }
        public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();
    }
}
