using System.ComponentModel.DataAnnotations;

namespace CarSalesSystem.Core.Models
{
    /// <summary>
    /// نموذج القسط
    /// </summary>
    public class Installment
    {
        [Key]
        public int InstallmentId { get; set; }

        [Required]
        public int SaleId { get; set; }

        [Required]
        public int InstallmentNumber { get; set; }

        [Required]
        public DateTime DueDate { get; set; }

        [Required]
        public decimal Amount { get; set; }

        public decimal PaidAmount { get; set; } = 0;

        [Required]
        public decimal RemainingAmount { get; set; }

        public bool IsPaid { get; set; } = false;

        public DateTime? PaidDate { get; set; }

        public decimal LateFee { get; set; } = 0;

        public int? PaymentId { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual Sale Sale { get; set; } = null!;
        public virtual Payment? Payment { get; set; }
    }
}
