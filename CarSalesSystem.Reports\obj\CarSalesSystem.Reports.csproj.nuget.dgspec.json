{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\برتامج\\CarSalesSystem.Reports\\CarSalesSystem.Reports.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\برتامج\\CarSalesSystem.Core\\CarSalesSystem.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\برتامج\\CarSalesSystem.Core\\CarSalesSystem.Core.csproj", "projectName": "CarSalesSystem.Core", "projectPath": "C:\\Users\\<USER>\\Desktop\\برتامج\\CarSalesSystem.Core\\CarSalesSystem.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\برتامج\\CarSalesSystem.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.411/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\برتامج\\CarSalesSystem.Reports\\CarSalesSystem.Reports.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\برتامج\\CarSalesSystem.Reports\\CarSalesSystem.Reports.csproj", "projectName": "CarSalesSystem.Reports", "projectPath": "C:\\Users\\<USER>\\Desktop\\برتامج\\CarSalesSystem.Reports\\CarSalesSystem.Reports.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\برتامج\\CarSalesSystem.Reports\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\برتامج\\CarSalesSystem.Core\\CarSalesSystem.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\برتامج\\CarSalesSystem.Core\\CarSalesSystem.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"iTextSharp": {"target": "Package", "version": "[5.5.13.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.411/PortableRuntimeIdentifierGraph.json"}}}}}