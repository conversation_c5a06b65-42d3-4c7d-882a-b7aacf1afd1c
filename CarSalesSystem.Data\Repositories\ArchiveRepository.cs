using CarSalesSystem.Core.Interfaces;
using CarSalesSystem.Core.Models;
using CarSalesSystem.Data.Context;
using Microsoft.EntityFrameworkCore;

namespace CarSalesSystem.Data.Repositories
{
    /// <summary>
    /// تنفيذ مستودع الأرشيف
    /// </summary>
    public class ArchiveRepository : GenericRepository<Archive>, IArchiveRepository
    {
        public ArchiveRepository(CarSalesDbContext context) : base(context)
        {
        }

        public IEnumerable<Archive> GetArchivesByEntity(string entityType, int entityId)
        {
            return _dbSet.Include(a => a.Uploader)
                         .Where(a => a.EntityType == entityType && a.EntityId == entityId && a.IsActive)
                         .OrderByDescending(a => a.UploadedDate)
                         .ToList();
        }

        public IEnumerable<Archive> GetArchivesByFileType(string fileType)
        {
            return _dbSet.Include(a => a.Uploader)
                         .Where(a => a.FileType == fileType && a.IsActive)
                         .OrderByDescending(a => a.UploadedDate)
                         .ToList();
        }

        public IEnumerable<Archive> SearchArchives(string searchTerm)
        {
            return _dbSet.Include(a => a.Uploader)
                         .Where(a => a.IsActive && 
                                   (a.OriginalFileName.Contains(searchTerm) ||
                                    a.Description != null && a.Description.Contains(searchTerm) ||
                                    a.EntityType.Contains(searchTerm)))
                         .OrderByDescending(a => a.UploadedDate)
                         .ToList();
        }

        public IEnumerable<Archive> GetActiveArchives()
        {
            return _dbSet.Include(a => a.Uploader)
                         .Where(a => a.IsActive)
                         .OrderByDescending(a => a.UploadedDate)
                         .ToList();
        }

        public IEnumerable<Archive> GetArchivesByUploader(int uploadedBy)
        {
            return _dbSet.Include(a => a.Uploader)
                         .Where(a => a.UploadedBy == uploadedBy && a.IsActive)
                         .OrderByDescending(a => a.UploadedDate)
                         .ToList();
        }

        public IEnumerable<Archive> GetArchivesByDateRange(DateTime startDate, DateTime endDate)
        {
            return _dbSet.Include(a => a.Uploader)
                         .Where(a => a.UploadedDate >= startDate && a.UploadedDate <= endDate && a.IsActive)
                         .OrderByDescending(a => a.UploadedDate)
                         .ToList();
        }

        public void DeactivateArchive(int archiveId)
        {
            var archive = _dbSet.Find(archiveId);
            if (archive != null)
            {
                archive.IsActive = false;
                _context.SaveChanges();
            }
        }

        public void ReactivateArchive(int archiveId)
        {
            var archive = _dbSet.Find(archiveId);
            if (archive != null)
            {
                archive.IsActive = true;
                _context.SaveChanges();
            }
        }

        public long GetTotalArchiveSize()
        {
            return _dbSet.Where(a => a.IsActive).Sum(a => a.FileSize);
        }

        public int GetArchiveFilesCount()
        {
            return _dbSet.Count(a => a.IsActive);
        }

        public override IEnumerable<Archive> GetAll()
        {
            return _dbSet.Include(a => a.Uploader)
                         .Where(a => a.IsActive)
                         .OrderByDescending(a => a.UploadedDate)
                         .ToList();
        }

        public override Archive? GetById(int id)
        {
            return _dbSet.Include(a => a.Uploader)
                         .FirstOrDefault(a => a.ArchiveId == id);
        }
    }
}
