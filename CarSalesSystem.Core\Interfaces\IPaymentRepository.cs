using CarSalesSystem.Core.Models;

namespace CarSalesSystem.Core.Interfaces
{
    /// <summary>
    /// واجهة مستودع الدفعات
    /// </summary>
    public interface IPaymentRepository : IGenericRepository<Payment>
    {
        /// <summary>
        /// الحصول على دفعة بواسطة رقم الدفعة
        /// </summary>
        /// <param name="paymentNumber">رقم الدفعة</param>
        /// <returns>الدفعة أو null</returns>
        Payment? GetByPaymentNumber(string paymentNumber);

        /// <summary>
        /// الحصول على دفعات المبيعة
        /// </summary>
        /// <param name="saleId">معرف المبيعة</param>
        /// <returns>قائمة الدفعات</returns>
        IEnumerable<Payment> GetPaymentsBySaleId(int saleId);

        /// <summary>
        /// الحصول على دفعات العميل
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <returns>قائمة الدفعات</returns>
        IEnumerable<Payment> GetPaymentsByCustomerId(int customerId);

        /// <summary>
        /// الحصول على الدفعات في فترة زمنية
        /// </summary>
        /// <param name="startDate">تاريخ البداية</param>
        /// <param name="endDate">تاريخ النهاية</param>
        /// <returns>قائمة الدفعات</returns>
        IEnumerable<Payment> GetPaymentsByDateRange(DateTime startDate, DateTime endDate);

        /// <summary>
        /// الحصول على الدفعات المستلمة بواسطة مستخدم
        /// </summary>
        /// <param name="receivedBy">معرف المستخدم</param>
        /// <returns>قائمة الدفعات</returns>
        IEnumerable<Payment> GetPaymentsByReceiver(int receivedBy);

        /// <summary>
        /// إنشاء رقم دفعة جديد
        /// </summary>
        /// <returns>رقم الدفعة الجديد</returns>
        string GeneratePaymentNumber();

        /// <summary>
        /// الحصول على إجمالي الدفعات لمبيعة
        /// </summary>
        /// <param name="saleId">معرف المبيعة</param>
        /// <returns>إجمالي الدفعات</returns>
        decimal GetTotalPaymentsForSale(int saleId);

        /// <summary>
        /// الحصول على إجمالي الدفعات في فترة
        /// </summary>
        /// <param name="startDate">تاريخ البداية</param>
        /// <param name="endDate">تاريخ النهاية</param>
        /// <returns>إجمالي الدفعات</returns>
        decimal GetTotalPaymentsAmount(DateTime startDate, DateTime endDate);
    }
}
