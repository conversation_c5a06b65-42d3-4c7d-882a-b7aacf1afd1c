using CarSalesSystem.Core.Interfaces;
using CarSalesSystem.Core.Models;
using CarSalesSystem.Core.Enums;
using CarSalesSystem.Data.Context;

namespace CarSalesSystem.Data.Repositories
{
    /// <summary>
    /// تنفيذ مستودع التراخيص
    /// </summary>
    public class LicenseRepository : GenericRepository<License>, ILicenseRepository
    {
        public LicenseRepository(CarSalesDbContext context) : base(context)
        {
        }

        public License? GetByLicenseKey(string licenseKey)
        {
            return _dbSet.FirstOrDefault(l => l.LicenseKey == licenseKey);
        }

        public License? GetByMachineId(string machineId)
        {
            return _dbSet.FirstOrDefault(l => l.MachineId == machineId && l.IsActive);
        }

        public IEnumerable<License> GetActiveLicenses()
        {
            return _dbSet.Where(l => l.IsActive).ToList();
        }

        public IEnumerable<License> GetExpiredLicenses()
        {
            var now = DateTime.Now;
            return _dbSet.Where(l => l.IsActive && 
                                   l.SubscriptionType != SubscriptionType.Lifetime && 
                                   l.EndDate.HasValue && 
                                   l.EndDate.Value < now)
                         .ToList();
        }

        public IEnumerable<License> GetLicensesBySubscriptionType(SubscriptionType subscriptionType)
        {
            return _dbSet.Where(l => l.SubscriptionType == subscriptionType).ToList();
        }

        public bool ValidateLicense(string licenseKey, string machineId)
        {
            var license = _dbSet.FirstOrDefault(l => l.LicenseKey == licenseKey);
            
            if (license == null || !license.IsActive)
                return false;

            if (license.MachineId != machineId)
                return false;

            if (license.SubscriptionType != SubscriptionType.Lifetime && 
                license.EndDate.HasValue && 
                license.EndDate.Value < DateTime.Now)
                return false;

            return true;
        }

        public bool ActivateLicense(string licenseKey, string machineId)
        {
            var license = _dbSet.FirstOrDefault(l => l.LicenseKey == licenseKey);
            
            if (license == null)
                return false;

            // التحقق من عدم تفعيل الترخيص على جهاز آخر
            if (!string.IsNullOrEmpty(license.MachineId) && license.MachineId != machineId)
                return false;

            license.MachineId = machineId;
            license.IsActive = true;
            license.ActivatedDate = DateTime.Now;
            
            _context.SaveChanges();
            return true;
        }

        public void DeactivateLicense(string licenseKey)
        {
            var license = _dbSet.FirstOrDefault(l => l.LicenseKey == licenseKey);
            if (license != null)
            {
                license.IsActive = false;
                license.DeactivatedDate = DateTime.Now;
                _context.SaveChanges();
            }
        }

        public bool IsLicenseExpired(string licenseKey)
        {
            var license = _dbSet.FirstOrDefault(l => l.LicenseKey == licenseKey);
            
            if (license == null)
                return true;

            if (license.SubscriptionType == SubscriptionType.Lifetime)
                return false;

            return license.EndDate.HasValue && license.EndDate.Value < DateTime.Now;
        }
    }
}
