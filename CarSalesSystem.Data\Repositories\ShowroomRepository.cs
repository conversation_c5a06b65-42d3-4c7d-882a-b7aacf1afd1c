using CarSalesSystem.Core.Interfaces;
using CarSalesSystem.Core.Models;
using CarSalesSystem.Data.Context;
using Microsoft.EntityFrameworkCore;

namespace CarSalesSystem.Data.Repositories
{
    /// <summary>
    /// تنفيذ مستودع معلومات المعرض
    /// </summary>
    public class ShowroomRepository : GenericRepository<ShowroomInfo>, IShowroomRepository
    {
        public ShowroomRepository(CarSalesDbContext context) : base(context)
        {
        }

        public ShowroomInfo? GetShowroomInfo()
        {
            return _dbSet.Include(s => s.Modifier).FirstOrDefault();
        }

        public void UpdateShowroomInfo(ShowroomInfo showroomInfo)
        {
            var existing = _dbSet.FirstOrDefault();
            if (existing != null)
            {
                // تحديث البيانات الموجودة
                existing.Name = showroomInfo.Name;
                existing.Address = showroomInfo.Address;
                existing.Phone = showroomInfo.Phone;
                existing.Email = showroomInfo.Email;
                existing.Website = showroomInfo.Website;
                existing.LogoPath = showroomInfo.LogoPath;
                existing.TaxNumber = showroomInfo.TaxNumber;
                existing.CommercialRegister = showroomInfo.CommercialRegister;
                existing.ModifiedBy = showroomInfo.ModifiedBy;
                existing.ModifiedDate = DateTime.Now;
            }
            else
            {
                // إضافة بيانات جديدة
                showroomInfo.ModifiedDate = DateTime.Now;
                _dbSet.Add(showroomInfo);
            }
            _context.SaveChanges();
        }

        public void UpdateLogo(string logoPath)
        {
            var showroomInfo = _dbSet.FirstOrDefault();
            if (showroomInfo != null)
            {
                showroomInfo.LogoPath = logoPath;
                showroomInfo.ModifiedDate = DateTime.Now;
                _context.SaveChanges();
            }
        }
    }
}
