using System.ComponentModel.DataAnnotations;

namespace CarSalesSystem.Core.Models
{
    /// <summary>
    /// نموذج إعدادات النظام
    /// </summary>
    public class SystemSettings
    {
        [Key]
        public int SettingId { get; set; }

        [Required]
        [StringLength(100)]
        public string SettingKey { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? SettingValue { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        public int? ModifiedBy { get; set; }

        public DateTime ModifiedDate { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual User? Modifier { get; set; }
    }
}
