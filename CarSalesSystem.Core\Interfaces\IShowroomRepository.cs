using CarSalesSystem.Core.Models;

namespace CarSalesSystem.Core.Interfaces
{
    /// <summary>
    /// واجهة مستودع معلومات المعرض
    /// </summary>
    public interface IShowroomRepository : IGenericRepository<ShowroomInfo>
    {
        /// <summary>
        /// الحصول على معلومات المعرض
        /// </summary>
        /// <returns>معلومات المعرض</returns>
        ShowroomInfo? GetShowroomInfo();

        /// <summary>
        /// تحديث معلومات المعرض
        /// </summary>
        /// <param name="showroomInfo">معلومات المعرض</param>
        void UpdateShowroomInfo(ShowroomInfo showroomInfo);

        /// <summary>
        /// تحديث لوجو المعرض
        /// </summary>
        /// <param name="logoPath">مسار اللوجو</param>
        void UpdateLogo(string logoPath);
    }
}
