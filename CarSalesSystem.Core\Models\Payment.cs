using System.ComponentModel.DataAnnotations;

namespace CarSalesSystem.Core.Models
{
    /// <summary>
    /// نموذج الدفعة
    /// </summary>
    public class Payment
    {
        [Key]
        public int PaymentId { get; set; }

        [Required]
        public int SaleId { get; set; }

        [Required]
        [StringLength(20)]
        public string PaymentNumber { get; set; } = string.Empty;

        public DateTime PaymentDate { get; set; } = DateTime.Now;

        [Required]
        public decimal Amount { get; set; }

        [StringLength(50)]
        public string? PaymentMethod { get; set; }

        [StringLength(50)]
        public string? ReferenceNumber { get; set; }

        [Required]
        public int ReceivedBy { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [StringLength(500)]
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual Sale Sale { get; set; } = null!;
        public virtual User Receiver { get; set; } = null!;
        public virtual ICollection<Installment> Installments { get; set; } = new List<Installment>();
    }
}
