using CarSalesSystem.Core.Interfaces;
using CarSalesSystem.Core.Models;
using CarSalesSystem.Core.Enums;
using CarSalesSystem.Data.Context;
using Microsoft.EntityFrameworkCore;

namespace CarSalesSystem.Data.Repositories
{
    /// <summary>
    /// تنفيذ مستودع المبيعات
    /// </summary>
    public class SaleRepository : GenericRepository<Sale>, ISaleRepository
    {
        public SaleRepository(CarSalesDbContext context) : base(context)
        {
        }

        public Sale? GetBySaleNumber(string saleNumber)
        {
            return _dbSet.Include(s => s.Customer)
                         .Include(s => s.Car)
                         .Include(s => s.SalesAgent)
                         .Include(s => s.Payments)
                         .Include(s => s.Installments)
                         .FirstOrDefault(s => s.SaleNumber == saleNumber);
        }

        public IEnumerable<Sale> GetSalesByCustomerId(int customerId)
        {
            return _dbSet.Include(s => s.Customer)
                         .Include(s => s.Car)
                         .Include(s => s.SalesAgent)
                         .Include(s => s.Payments)
                         .Include(s => s.Installments)
                         .Where(s => s.CustomerId == customerId)
                         .ToList();
        }

        public IEnumerable<Sale> GetSalesByCarId(int carId)
        {
            return _dbSet.Include(s => s.Customer)
                         .Include(s => s.Car)
                         .Include(s => s.SalesAgent)
                         .Include(s => s.Payments)
                         .Include(s => s.Installments)
                         .Where(s => s.CarId == carId)
                         .ToList();
        }

        public IEnumerable<Sale> GetSalesBySalesAgent(int salesAgentId)
        {
            return _dbSet.Include(s => s.Customer)
                         .Include(s => s.Car)
                         .Include(s => s.SalesAgent)
                         .Include(s => s.Payments)
                         .Include(s => s.Installments)
                         .Where(s => s.SalesAgentId == salesAgentId)
                         .ToList();
        }

        public IEnumerable<Sale> GetSalesByPaymentMethod(PaymentMethod paymentMethod)
        {
            return _dbSet.Include(s => s.Customer)
                         .Include(s => s.Car)
                         .Include(s => s.SalesAgent)
                         .Include(s => s.Payments)
                         .Include(s => s.Installments)
                         .Where(s => s.PaymentMethod == paymentMethod)
                         .ToList();
        }

        public IEnumerable<Sale> GetSalesByDateRange(DateTime startDate, DateTime endDate)
        {
            return _dbSet.Include(s => s.Customer)
                         .Include(s => s.Car)
                         .Include(s => s.SalesAgent)
                         .Include(s => s.Payments)
                         .Include(s => s.Installments)
                         .Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                         .ToList();
        }

        public IEnumerable<Sale> GetCompletedSales()
        {
            return _dbSet.Include(s => s.Customer)
                         .Include(s => s.Car)
                         .Include(s => s.SalesAgent)
                         .Include(s => s.Payments)
                         .Include(s => s.Installments)
                         .Where(s => s.IsCompleted)
                         .ToList();
        }

        public IEnumerable<Sale> GetIncompleteSales()
        {
            return _dbSet.Include(s => s.Customer)
                         .Include(s => s.Car)
                         .Include(s => s.SalesAgent)
                         .Include(s => s.Payments)
                         .Include(s => s.Installments)
                         .Where(s => !s.IsCompleted)
                         .ToList();
        }

        public string GenerateSaleNumber()
        {
            var today = DateTime.Now;
            var prefix = $"S{today:yyyyMMdd}";
            
            var lastSale = _dbSet.Where(s => s.SaleNumber.StartsWith(prefix))
                                 .OrderByDescending(s => s.SaleNumber)
                                 .FirstOrDefault();

            int nextSequence = 1;
            if (lastSale != null)
            {
                var lastSequence = lastSale.SaleNumber.Substring(prefix.Length);
                if (int.TryParse(lastSequence, out int seq))
                {
                    nextSequence = seq + 1;
                }
            }

            return $"{prefix}{nextSequence:D3}"; // مثال: S20241201001
        }

        public decimal GetTotalSalesAmount(DateTime startDate, DateTime endDate)
        {
            return _dbSet.Where(s => s.SaleDate >= startDate && s.SaleDate <= endDate)
                         .Sum(s => s.TotalAmount);
        }

        public override IEnumerable<Sale> GetAll()
        {
            return _dbSet.Include(s => s.Customer)
                         .Include(s => s.Car)
                         .Include(s => s.SalesAgent)
                         .Include(s => s.Payments)
                         .Include(s => s.Installments)
                         .ToList();
        }

        public override Sale? GetById(int id)
        {
            return _dbSet.Include(s => s.Customer)
                         .Include(s => s.Car)
                         .Include(s => s.SalesAgent)
                         .Include(s => s.Payments)
                         .Include(s => s.Installments)
                         .FirstOrDefault(s => s.SaleId == id);
        }
    }
}
