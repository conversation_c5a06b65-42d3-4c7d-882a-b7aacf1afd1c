using CarSalesSystem.Core.Interfaces;
using CarSalesSystem.Core.Models;
using CarSalesSystem.Data.Context;
using Microsoft.EntityFrameworkCore;

namespace CarSalesSystem.Data.Repositories
{
    /// <summary>
    /// تنفيذ مستودع الأقساط
    /// </summary>
    public class InstallmentRepository : GenericRepository<Installment>, IInstallmentRepository
    {
        public InstallmentRepository(CarSalesDbContext context) : base(context)
        {
        }

        public IEnumerable<Installment> GetInstallmentsBySaleId(int saleId)
        {
            return _dbSet.Include(i => i.Sale)
                         .ThenInclude(s => s.Customer)
                         .Include(i => i.Sale)
                         .ThenInclude(s => s.Car)
                         .Include(i => i.Payment)
                         .Where(i => i.SaleId == saleId)
                         .OrderBy(i => i.InstallmentNumber)
                         .ToList();
        }

        public IEnumerable<Installment> GetDueInstallments()
        {
            var today = DateTime.Now.Date;
            return _dbSet.Include(i => i.Sale)
                         .ThenInclude(s => s.Customer)
                         .Include(i => i.Sale)
                         .ThenInclude(s => s.Car)
                         .Include(i => i.Payment)
                         .Where(i => !i.IsPaid && i.DueDate.Date <= today)
                         .OrderBy(i => i.DueDate)
                         .ToList();
        }

        public IEnumerable<Installment> GetOverdueInstallments()
        {
            var today = DateTime.Now.Date;
            return _dbSet.Include(i => i.Sale)
                         .ThenInclude(s => s.Customer)
                         .Include(i => i.Sale)
                         .ThenInclude(s => s.Car)
                         .Include(i => i.Payment)
                         .Where(i => !i.IsPaid && i.DueDate.Date < today)
                         .OrderBy(i => i.DueDate)
                         .ToList();
        }

        public IEnumerable<Installment> GetPaidInstallments(int saleId)
        {
            return _dbSet.Include(i => i.Sale)
                         .ThenInclude(s => s.Customer)
                         .Include(i => i.Sale)
                         .ThenInclude(s => s.Car)
                         .Include(i => i.Payment)
                         .Where(i => i.SaleId == saleId && i.IsPaid)
                         .OrderBy(i => i.InstallmentNumber)
                         .ToList();
        }

        public IEnumerable<Installment> GetUnpaidInstallments(int saleId)
        {
            return _dbSet.Include(i => i.Sale)
                         .ThenInclude(s => s.Customer)
                         .Include(i => i.Sale)
                         .ThenInclude(s => s.Car)
                         .Include(i => i.Payment)
                         .Where(i => i.SaleId == saleId && !i.IsPaid)
                         .OrderBy(i => i.InstallmentNumber)
                         .ToList();
        }

        public IEnumerable<Installment> GetDueInstallmentsByCustomer(int customerId)
        {
            var today = DateTime.Now.Date;
            return _dbSet.Include(i => i.Sale)
                         .ThenInclude(s => s.Customer)
                         .Include(i => i.Sale)
                         .ThenInclude(s => s.Car)
                         .Include(i => i.Payment)
                         .Where(i => i.Sale.CustomerId == customerId && !i.IsPaid && i.DueDate.Date <= today)
                         .OrderBy(i => i.DueDate)
                         .ToList();
        }

        public IEnumerable<Installment> GetInstallmentsByDateRange(DateTime startDate, DateTime endDate)
        {
            return _dbSet.Include(i => i.Sale)
                         .ThenInclude(s => s.Customer)
                         .Include(i => i.Sale)
                         .ThenInclude(s => s.Car)
                         .Include(i => i.Payment)
                         .Where(i => i.DueDate >= startDate && i.DueDate <= endDate)
                         .OrderBy(i => i.DueDate)
                         .ToList();
        }

        public void MarkAsPaid(int installmentId, int paymentId, decimal paidAmount)
        {
            var installment = _dbSet.Find(installmentId);
            if (installment != null)
            {
                installment.IsPaid = true;
                installment.PaidDate = DateTime.Now;
                installment.PaymentId = paymentId;
                installment.PaidAmount = paidAmount;
                installment.RemainingAmount = installment.Amount - paidAmount;
                _context.SaveChanges();
            }
        }

        public void CreateInstallmentSchedule(int saleId, decimal totalAmount, decimal monthlyInstallment, int numberOfMonths, DateTime startDate)
        {
            var installments = new List<Installment>();
            var currentDate = startDate;

            for (int i = 1; i <= numberOfMonths; i++)
            {
                var installmentAmount = (i == numberOfMonths) ? 
                    totalAmount - (monthlyInstallment * (numberOfMonths - 1)) : // القسط الأخير يأخذ الباقي
                    monthlyInstallment;

                var installment = new Installment
                {
                    SaleId = saleId,
                    InstallmentNumber = i,
                    DueDate = currentDate,
                    Amount = installmentAmount,
                    RemainingAmount = installmentAmount,
                    CreatedDate = DateTime.Now
                };

                installments.Add(installment);
                currentDate = currentDate.AddMonths(1);
            }

            _dbSet.AddRange(installments);
            _context.SaveChanges();
        }

        public decimal GetRemainingInstallmentsAmount(int saleId)
        {
            return _dbSet.Where(i => i.SaleId == saleId && !i.IsPaid)
                         .Sum(i => i.RemainingAmount);
        }

        public override IEnumerable<Installment> GetAll()
        {
            return _dbSet.Include(i => i.Sale)
                         .ThenInclude(s => s.Customer)
                         .Include(i => i.Sale)
                         .ThenInclude(s => s.Car)
                         .Include(i => i.Payment)
                         .ToList();
        }

        public override Installment? GetById(int id)
        {
            return _dbSet.Include(i => i.Sale)
                         .ThenInclude(s => s.Customer)
                         .Include(i => i.Sale)
                         .ThenInclude(s => s.Car)
                         .Include(i => i.Payment)
                         .FirstOrDefault(i => i.InstallmentId == id);
        }
    }
}
