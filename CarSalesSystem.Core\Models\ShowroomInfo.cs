using System.ComponentModel.DataAnnotations;

namespace CarSalesSystem.Core.Models
{
    /// <summary>
    /// نموذج معلومات المعرض
    /// </summary>
    public class ShowroomInfo
    {
        [Key]
        public int ShowroomId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Address { get; set; }

        [StringLength(50)]
        public string? Phone { get; set; }

        [StringLength(100)]
        public string? Email { get; set; }

        [StringLength(100)]
        public string? Website { get; set; }

        [StringLength(500)]
        public string? LogoPath { get; set; }

        [StringLength(50)]
        public string? TaxNumber { get; set; }

        [StringLength(50)]
        public string? CommercialRegister { get; set; }

        public int? ModifiedBy { get; set; }

        public DateTime ModifiedDate { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual User? Modifier { get; set; }
    }
}
