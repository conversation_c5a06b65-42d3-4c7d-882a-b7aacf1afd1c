using CarSalesSystem.Core.Interfaces;
using CarSalesSystem.Core.Models;
using CarSalesSystem.Core.Enums;
using CarSalesSystem.Data.Context;
using Microsoft.EntityFrameworkCore;

namespace CarSalesSystem.Data.Repositories
{
    /// <summary>
    /// تنفيذ مستودع المستخدمين
    /// </summary>
    public class UserRepository : GenericRepository<User>, IUserRepository
    {
        public UserRepository(CarSalesDbContext context) : base(context)
        {
        }

        public User? GetByUsername(string username)
        {
            return _dbSet.FirstOrDefault(u => u.Username == username);
        }

        public IEnumerable<User> GetUsersByRole(UserRole role)
        {
            return _dbSet.Where(u => u.Role == role && u.IsActive).ToList();
        }

        public bool IsUsernameExists(string username)
        {
            return _dbSet.Any(u => u.Username == username);
        }

        public IEnumerable<User> GetActiveUsers()
        {
            return _dbSet.Where(u => u.IsActive).ToList();
        }

        public void UpdateLastLoginDate(int userId)
        {
            var user = _dbSet.Find(userId);
            if (user != null)
            {
                user.LastLoginDate = DateTime.Now;
                _context.SaveChanges();
            }
        }

        public override IEnumerable<User> GetAll()
        {
            return _dbSet.Include(u => u.Creator).ToList();
        }

        public override User? GetById(int id)
        {
            return _dbSet.Include(u => u.Creator).FirstOrDefault(u => u.UserId == id);
        }
    }
}
