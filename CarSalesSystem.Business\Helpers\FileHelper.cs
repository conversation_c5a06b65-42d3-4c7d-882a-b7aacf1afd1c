using System.IO.Compression;

namespace CarSalesSystem.Business.Helpers
{
    /// <summary>
    /// مساعد إدارة الملفات
    /// </summary>
    public static class FileHelper
    {
        /// <summary>
        /// إنشاء مجلد إذا لم يكن موجوداً
        /// </summary>
        /// <param name="path">مسار المجلد</param>
        public static void EnsureDirectoryExists(string path)
        {
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
        }

        /// <summary>
        /// نسخ ملف مع إنشاء المجلد الهدف إذا لم يكن موجوداً
        /// </summary>
        /// <param name="sourcePath">مسار الملف المصدر</param>
        /// <param name="destinationPath">مسار الملف الهدف</param>
        /// <param name="overwrite">استبدال الملف إذا كان موجوداً</param>
        public static void CopyFile(string sourcePath, string destinationPath, bool overwrite = true)
        {
            var destinationDirectory = Path.GetDirectoryName(destinationPath);
            if (!string.IsNullOrEmpty(destinationDirectory))
            {
                EnsureDirectoryExists(destinationDirectory);
            }

            File.Copy(sourcePath, destinationPath, overwrite);
        }

        /// <summary>
        /// حذف ملف بأمان
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteFile(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على حجم الملف بالبايت
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>حجم الملف</returns>
        public static long GetFileSize(string filePath)
        {
            if (File.Exists(filePath))
            {
                var fileInfo = new FileInfo(filePath);
                return fileInfo.Length;
            }
            return 0;
        }

        /// <summary>
        /// تحويل حجم الملف إلى نص قابل للقراءة
        /// </summary>
        /// <param name="bytes">الحجم بالبايت</param>
        /// <returns>النص المنسق</returns>
        public static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// إنشاء اسم ملف فريد
        /// </summary>
        /// <param name="originalFileName">اسم الملف الأصلي</param>
        /// <returns>اسم الملف الفريد</returns>
        public static string GenerateUniqueFileName(string originalFileName)
        {
            var extension = Path.GetExtension(originalFileName);
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            var guid = Guid.NewGuid().ToString("N")[..8];
            
            return $"{nameWithoutExtension}_{timestamp}_{guid}{extension}";
        }

        /// <summary>
        /// التحقق من صحة امتداد الملف
        /// </summary>
        /// <param name="fileName">اسم الملف</param>
        /// <param name="allowedExtensions">الامتدادات المسموحة</param>
        /// <returns>true إذا كان الامتداد مسموح</returns>
        public static bool IsValidFileExtension(string fileName, string[] allowedExtensions)
        {
            var extension = Path.GetExtension(fileName).ToLower();
            return allowedExtensions.Contains(extension);
        }

        /// <summary>
        /// ضغط مجلد إلى ملف ZIP
        /// </summary>
        /// <param name="sourceDirectory">مسار المجلد المصدر</param>
        /// <param name="destinationZipFile">مسار ملف ZIP الهدف</param>
        public static void CompressDirectory(string sourceDirectory, string destinationZipFile)
        {
            var destinationDirectory = Path.GetDirectoryName(destinationZipFile);
            if (!string.IsNullOrEmpty(destinationDirectory))
            {
                EnsureDirectoryExists(destinationDirectory);
            }

            if (File.Exists(destinationZipFile))
            {
                File.Delete(destinationZipFile);
            }

            ZipFile.CreateFromDirectory(sourceDirectory, destinationZipFile);
        }

        /// <summary>
        /// استخراج ملف ZIP إلى مجلد
        /// </summary>
        /// <param name="sourceZipFile">مسار ملف ZIP المصدر</param>
        /// <param name="destinationDirectory">مسار المجلد الهدف</param>
        public static void ExtractZipFile(string sourceZipFile, string destinationDirectory)
        {
            EnsureDirectoryExists(destinationDirectory);
            ZipFile.ExtractToDirectory(sourceZipFile, destinationDirectory);
        }

        /// <summary>
        /// الحصول على قائمة الملفات في مجلد مع فلترة
        /// </summary>
        /// <param name="directoryPath">مسار المجلد</param>
        /// <param name="searchPattern">نمط البحث</param>
        /// <param name="includeSubdirectories">تضمين المجلدات الفرعية</param>
        /// <returns>قائمة الملفات</returns>
        public static string[] GetFiles(string directoryPath, string searchPattern = "*.*", bool includeSubdirectories = false)
        {
            if (!Directory.Exists(directoryPath))
            {
                return Array.Empty<string>();
            }

            var searchOption = includeSubdirectories ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
            return Directory.GetFiles(directoryPath, searchPattern, searchOption);
        }

        /// <summary>
        /// تنظيف اسم الملف من الأحرف غير المسموحة
        /// </summary>
        /// <param name="fileName">اسم الملف</param>
        /// <returns>اسم الملف المنظف</returns>
        public static string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = fileName;
            
            foreach (var invalidChar in invalidChars)
            {
                sanitized = sanitized.Replace(invalidChar, '_');
            }
            
            return sanitized;
        }

        /// <summary>
        /// نسخ احتياطي لملف
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="backupDirectory">مجلد النسخ الاحتياطية</param>
        /// <returns>مسار النسخة الاحتياطية</returns>
        public static string BackupFile(string filePath, string backupDirectory)
        {
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException("الملف غير موجود", filePath);
            }

            EnsureDirectoryExists(backupDirectory);
            
            var fileName = Path.GetFileName(filePath);
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            var backupFileName = $"{Path.GetFileNameWithoutExtension(fileName)}_{timestamp}{Path.GetExtension(fileName)}";
            var backupPath = Path.Combine(backupDirectory, backupFileName);
            
            File.Copy(filePath, backupPath);
            return backupPath;
        }
    }
}
