using CarSalesSystem.Core.Enums;
using System.ComponentModel.DataAnnotations;

namespace CarSalesSystem.Core.Models
{
    /// <summary>
    /// نموذج الترخيص
    /// </summary>
    public class License
    {
        [Key]
        public int LicenseId { get; set; }

        [Required]
        [StringLength(255)]
        public string LicenseKey { get; set; } = string.Empty;

        [Required]
        public SubscriptionType SubscriptionType { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public bool IsActive { get; set; } = true;

        [Required]
        [StringLength(255)]
        public string MachineId { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ActivatedDate { get; set; }

        public DateTime? DeactivatedDate { get; set; }
    }
}
