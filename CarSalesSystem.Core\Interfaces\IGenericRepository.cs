using System.Linq.Expressions;

namespace CarSalesSystem.Core.Interfaces
{
    /// <summary>
    /// واجهة المستودع العامة
    /// </summary>
    /// <typeparam name="T">نوع الكيان</typeparam>
    public interface IGenericRepository<T> where T : class
    {
        /// <summary>
        /// الحصول على كيان بواسطة المعرف
        /// </summary>
        /// <param name="id">المعرف</param>
        /// <returns>الكيان أو null</returns>
        T? GetById(int id);

        /// <summary>
        /// الحصول على جميع الكيانات
        /// </summary>
        /// <returns>قائمة الكيانات</returns>
        IEnumerable<T> GetAll();

        /// <summary>
        /// البحث عن الكيانات بناءً على شرط
        /// </summary>
        /// <param name="expression">الشرط</param>
        /// <returns>قائمة الكيانات المطابقة</returns>
        IEnumerable<T> Find(Expression<Func<T, bool>> expression);

        /// <summary>
        /// إضافة كيان جديد
        /// </summary>
        /// <param name="entity">الكيان</param>
        void Add(T entity);

        /// <summary>
        /// إضافة مجموعة من الكيانات
        /// </summary>
        /// <param name="entities">الكيانات</param>
        void AddRange(IEnumerable<T> entities);

        /// <summary>
        /// تحديث كيان
        /// </summary>
        /// <param name="entity">الكيان</param>
        void Update(T entity);

        /// <summary>
        /// حذف كيان
        /// </summary>
        /// <param name="entity">الكيان</param>
        void Remove(T entity);

        /// <summary>
        /// حذف مجموعة من الكيانات
        /// </summary>
        /// <param name="entities">الكيانات</param>
        void RemoveRange(IEnumerable<T> entities);

        /// <summary>
        /// حفظ التغييرات
        /// </summary>
        /// <returns>عدد السجلات المتأثرة</returns>
        int SaveChanges();

        /// <summary>
        /// حفظ التغييرات بشكل غير متزامن
        /// </summary>
        /// <returns>عدد السجلات المتأثرة</returns>
        Task<int> SaveChangesAsync();
    }
}
