using CarSalesSystem.Core.Models;
using CarSalesSystem.Core.Enums;

namespace CarSalesSystem.Core.Interfaces
{
    /// <summary>
    /// واجهة مستودع التراخيص
    /// </summary>
    public interface ILicenseRepository : IGenericRepository<License>
    {
        /// <summary>
        /// الحصول على ترخيص بواسطة مفتاح الترخيص
        /// </summary>
        /// <param name="licenseKey">مفتاح الترخيص</param>
        /// <returns>الترخيص أو null</returns>
        License? GetByLicenseKey(string licenseKey);

        /// <summary>
        /// الحصول على ترخيص بواسطة معرف الجهاز
        /// </summary>
        /// <param name="machineId">معرف الجهاز</param>
        /// <returns>الترخيص أو null</returns>
        License? GetByMachineId(string machineId);

        /// <summary>
        /// الحصول على التراخيص النشطة
        /// </summary>
        /// <returns>قائمة التراخيص النشطة</returns>
        IEnumerable<License> GetActiveLicenses();

        /// <summary>
        /// الحصول على التراخيص المنتهية الصلاحية
        /// </summary>
        /// <returns>قائمة التراخيص المنتهية</returns>
        IEnumerable<License> GetExpiredLicenses();

        /// <summary>
        /// الحصول على التراخيص حسب نوع الاشتراك
        /// </summary>
        /// <param name="subscriptionType">نوع الاشتراك</param>
        /// <returns>قائمة التراخيص</returns>
        IEnumerable<License> GetLicensesBySubscriptionType(SubscriptionType subscriptionType);

        /// <summary>
        /// التحقق من صحة الترخيص
        /// </summary>
        /// <param name="licenseKey">مفتاح الترخيص</param>
        /// <param name="machineId">معرف الجهاز</param>
        /// <returns>true إذا كان الترخيص صحيح</returns>
        bool ValidateLicense(string licenseKey, string machineId);

        /// <summary>
        /// تفعيل ترخيص
        /// </summary>
        /// <param name="licenseKey">مفتاح الترخيص</param>
        /// <param name="machineId">معرف الجهاز</param>
        /// <returns>true إذا تم التفعيل بنجاح</returns>
        bool ActivateLicense(string licenseKey, string machineId);

        /// <summary>
        /// إلغاء تفعيل ترخيص
        /// </summary>
        /// <param name="licenseKey">مفتاح الترخيص</param>
        void DeactivateLicense(string licenseKey);

        /// <summary>
        /// التحقق من انتهاء صلاحية الترخيص
        /// </summary>
        /// <param name="licenseKey">مفتاح الترخيص</param>
        /// <returns>true إذا انتهت الصلاحية</returns>
        bool IsLicenseExpired(string licenseKey);
    }
}
