using System.ComponentModel.DataAnnotations;

namespace CarSalesSystem.Core.Models
{
    /// <summary>
    /// نموذج الأرشيف
    /// </summary>
    public class Archive
    {
        [Key]
        public int ArchiveId { get; set; }

        [Required]
        [StringLength(50)]
        public string EntityType { get; set; } = string.Empty;

        [Required]
        public int EntityId { get; set; }

        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string OriginalFileName { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;

        [Required]
        public long FileSize { get; set; }

        [StringLength(50)]
        public string? FileType { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        [Required]
        public int UploadedBy { get; set; }

        public DateTime UploadedDate { get; set; } = DateTime.Now;

        public bool IsActive { get; set; } = true;

        // Navigation Properties
        public virtual User Uploader { get; set; } = null!;
    }
}
