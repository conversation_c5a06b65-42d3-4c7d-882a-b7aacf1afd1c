using System.Drawing;
using System.Drawing.Printing;

namespace CarSalesSystem.Business.Helpers
{
    /// <summary>
    /// مساعد الطباعة
    /// </summary>
    public static class PrintHelper
    {
        /// <summary>
        /// طباعة نص
        /// </summary>
        /// <param name="text">النص المراد طباعته</param>
        /// <param name="documentName">اسم المستند</param>
        /// <param name="showPrintDialog">إظهار حوار الطباعة</param>
        public static void PrintText(string text, string documentName = "Document", bool showPrintDialog = true)
        {
            try
            {
                var printDocument = new PrintDocument();
                printDocument.DocumentName = documentName;

                printDocument.PrintPage += (sender, e) =>
                {
                    if (e.Graphics != null)
                    {
                        var font = new Font("Arial", 12);
                        var brush = new SolidBrush(Color.Black);
                        var rect = e.MarginBounds;

                        e.Graphics.DrawString(text, font, brush, rect);
                    }
                };

                if (showPrintDialog)
                {
                    var printDialog = new PrintDialog();
                    printDialog.Document = printDocument;

                    if (printDialog.ShowDialog() == DialogResult.OK)
                    {
                        printDocument.Print();
                    }
                }
                else
                {
                    printDocument.Print();
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في الطباعة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على قائمة الطابعات المتاحة
        /// </summary>
        /// <returns>قائمة أسماء الطابعات</returns>
        public static string[] GetAvailablePrinters()
        {
            var printers = new List<string>();
            
            foreach (string printerName in PrinterSettings.InstalledPrinters)
            {
                printers.Add(printerName);
            }
            
            return printers.ToArray();
        }

        /// <summary>
        /// الحصول على الطابعة الافتراضية
        /// </summary>
        /// <returns>اسم الطابعة الافتراضية</returns>
        public static string GetDefaultPrinter()
        {
            var printerSettings = new PrinterSettings();
            return printerSettings.PrinterName;
        }

        /// <summary>
        /// التحقق من توفر طابعة
        /// </summary>
        /// <param name="printerName">اسم الطابعة</param>
        /// <returns>true إذا كانت متوفرة</returns>
        public static bool IsPrinterAvailable(string printerName)
        {
            return PrinterSettings.InstalledPrinters.Cast<string>().Contains(printerName);
        }

        /// <summary>
        /// طباعة صورة
        /// </summary>
        /// <param name="imagePath">مسار الصورة</param>
        /// <param name="documentName">اسم المستند</param>
        /// <param name="showPrintDialog">إظهار حوار الطباعة</param>
        public static void PrintImage(string imagePath, string documentName = "Image", bool showPrintDialog = true)
        {
            if (!File.Exists(imagePath))
            {
                throw new FileNotFoundException("ملف الصورة غير موجود", imagePath);
            }

            try
            {
                var printDocument = new PrintDocument();
                printDocument.DocumentName = documentName;

                printDocument.PrintPage += (sender, e) =>
                {
                    if (e.Graphics != null)
                    {
                        using (var image = Image.FromFile(imagePath))
                        {
                            var rect = e.MarginBounds;
                            
                            // تحديد حجم الصورة للطباعة مع الحفاظ على النسبة
                            var imageSize = GetScaledImageSize(image.Size, rect.Size);
                            var x = rect.X + (rect.Width - imageSize.Width) / 2;
                            var y = rect.Y + (rect.Height - imageSize.Height) / 2;
                            
                            e.Graphics.DrawImage(image, x, y, imageSize.Width, imageSize.Height);
                        }
                    }
                };

                if (showPrintDialog)
                {
                    var printDialog = new PrintDialog();
                    printDialog.Document = printDocument;

                    if (printDialog.ShowDialog() == DialogResult.OK)
                    {
                        printDocument.Print();
                    }
                }
                else
                {
                    printDocument.Print();
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في طباعة الصورة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حساب حجم الصورة المقيس للطباعة
        /// </summary>
        /// <param name="originalSize">الحجم الأصلي</param>
        /// <param name="targetSize">الحجم المستهدف</param>
        /// <returns>الحجم المقيس</returns>
        private static Size GetScaledImageSize(Size originalSize, Size targetSize)
        {
            var scaleX = (double)targetSize.Width / originalSize.Width;
            var scaleY = (double)targetSize.Height / originalSize.Height;
            var scale = Math.Min(scaleX, scaleY);

            return new Size(
                (int)(originalSize.Width * scale),
                (int)(originalSize.Height * scale)
            );
        }

        /// <summary>
        /// معاينة الطباعة
        /// </summary>
        /// <param name="printDocument">مستند الطباعة</param>
        public static void ShowPrintPreview(PrintDocument printDocument)
        {
            try
            {
                var printPreviewDialog = new PrintPreviewDialog();
                printPreviewDialog.Document = printDocument;
                printPreviewDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في معاينة الطباعة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إعداد صفحة الطباعة
        /// </summary>
        /// <param name="printDocument">مستند الطباعة</param>
        public static void ShowPageSetup(PrintDocument printDocument)
        {
            try
            {
                var pageSetupDialog = new PageSetupDialog();
                pageSetupDialog.Document = printDocument;
                pageSetupDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في إعداد الصفحة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحويل ملف PDF إلى صور للطباعة (يتطلب مكتبة إضافية)
        /// </summary>
        /// <param name="pdfPath">مسار ملف PDF</param>
        /// <param name="outputDirectory">مجلد الإخراج</param>
        /// <returns>قائمة مسارات الصور</returns>
        public static string[] ConvertPdfToImages(string pdfPath, string outputDirectory)
        {
            // هذه الوظيفة تحتاج إلى مكتبة إضافية مثل iTextSharp أو PdfiumViewer
            // للتبسيط، سنعيد قائمة فارغة
            return Array.Empty<string>();
        }

        /// <summary>
        /// طباعة عدة صفحات
        /// </summary>
        /// <param name="pages">قائمة النصوص للصفحات</param>
        /// <param name="documentName">اسم المستند</param>
        /// <param name="showPrintDialog">إظهار حوار الطباعة</param>
        public static void PrintMultiplePages(string[] pages, string documentName = "Multi-Page Document", bool showPrintDialog = true)
        {
            try
            {
                var printDocument = new PrintDocument();
                printDocument.DocumentName = documentName;
                
                int currentPage = 0;

                printDocument.PrintPage += (sender, e) =>
                {
                    if (e.Graphics != null && currentPage < pages.Length)
                    {
                        var font = new Font("Arial", 12);
                        var brush = new SolidBrush(Color.Black);
                        var rect = e.MarginBounds;

                        e.Graphics.DrawString(pages[currentPage], font, brush, rect);
                        
                        currentPage++;
                        e.HasMorePages = currentPage < pages.Length;
                    }
                };

                if (showPrintDialog)
                {
                    var printDialog = new PrintDialog();
                    printDialog.Document = printDocument;

                    if (printDialog.ShowDialog() == DialogResult.OK)
                    {
                        printDocument.Print();
                    }
                }
                else
                {
                    printDocument.Print();
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في طباعة الصفحات المتعددة: {ex.Message}", ex);
            }
        }
    }
}
